{"\"meta\"": "{", "\"instanceId\"": "\"408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9\",", "\"templateCredsSetupCompleted\"": "true", "\"nodes\"": "[", "\"id\"": "\"23bca6e2-e16a-48a4-a7fc-96ce25846764\",", "\"name\"": "\"Sticky Note20\",", "\"type\"": "\"ai_outputParser\",", "\"position\"": "[", "\"parameters\"": "{", "\"schemaType\"": "\"manual\",", "\"inputSchema\"": "\"{\\n \\\"type\\\": \\\"object\\\",\\n \\\"properties\\\": {\\n \\\"title\\\": {\\n \\\"type\\\": \\\"string\\\",\\n \\\"description\\\":\\\" A short title summarising the research topic\\\"\\n },\\n \\\"description\\\": {\\n \\\"type\\\": \\\"string\\\",\\n \\\"description\\\": \\\"A short description to summarise the research topic\\\"\\n }\\n }\\n}\"", "\"typeVersion\"": "1", "\"options\"": "{}", "\"assignments\"": "[", "\"value\"": "\"={{ [] }}\"", "\"model\"": "{", "\"__rl\"": "true,", "\"mode\"": "\"id\",", "\"credentials\"": "{", "\"openAiApi\"": "{", "\"webhookId\"": "\"d4ea875f-83cb-49a8-8992-c08b4114c9bd\",", "\"path\"": "\"deep_research\",", "\"ignoreBots\"": "true,", "\"buttonLabel\"": "\"Done\",", "\"formTitle\"": "\"DeepResearcher\",", "\"formFields\"": "{", "\"values\"": "[", "\"fieldType\"": "\"dropdown\",", "\"fieldLabel\"": "\"={{ \\\"\\\" }}\",", "\"formDescription\"": "\"=<img\\n src=\\\"https://res.cloudinary.com/daglih2g8/image/upload/f_auto,q_auto/v1/n8n-workflows/o4wqztloz3j6okfxpeyw\\\"\\n width=\\\"100%\\\"\\n style=\\\"border:1px solid #ccc\\\"\\n/>\"", "\"text\"": "\"=Given the following contents from a SERP search for the query <query>{{ $('Item Ref').first().json.query }}</query>, generate a list of learnings from the contents. Return a maximum of 3 learnings, but feel free to return less if the contents are clear. Make sure each learning is unique and not similar to each other. The learnings should be concise and to the point, as detailed and infromation dense as possible. Make sure to include any entities like people, places, companies, products, things, etc in the learnings, as well as any exact metrics, numbers, or dates. The learnings will be used to research the topic further.\\n\\n<contents>\\n{{\\n$('Convert to Markdown')\\n .all()\\n .map(item =>`<content>\\\\n${item.json.markdown.substr(0, 25_000)}\\\\n</content>`)\\n .join('\\\\n')\\n}}\\n</contents>\",", "\"messages\"": "{", "\"messageValues\"": "[", "\"message\"": "\"=You are an expert researcher. Today is {{ $now.toLocaleString() }}. Follow these instructions when responding:\\n - You may be asked to research subjects that is after your knowledge cutoff, assume the user is right when presented with news.\\n - The user is a highly experienced analyst, no need to simplify it, be as detailed as possible and make sure your response is correct.\\n - Be highly organized.\\n - Suggest solutions that I didn't think about.\\n - Be proactive and anticipate my needs.\\n - Treat me as an expert in all subject matter.\\n - Mistakes erode my trust, so be accurate and thorough.\\n - Provide detailed explanations, I'm comfortable with lots of detail.\\n - Value good arguments over authorities, the source is irrelevant.\\n - Consider new technologies and contrarian ideas, not just the conventional wisdom.\\n - You may use high levels of speculation or prediction, just flag it for me.\"", "\"promptType\"": "\"define\",", "\"hasOutputParser\"": "true", "\"fieldToSplitOut\"": "\"tag\"", "\"executeOnce\"": "true,", "\"jsonOutput\"": "\"={{ $('Generate Learnings').item.json }}\"", "\"onError\"": "\"continueRegularOutput\",", "\"url\"": "\"=https://api.notion.com/v1/blocks/{{ $('Get Existing Row1').first().json.id }}/children\",", "\"method\"": "\"PATCH\",", "\"sendBody\"": "true,", "\"authentication\"": "\"predefinedCredentialType\",", "\"bodyParameters\"": "{", "\"genericAuthType\"": "\"httpQueryAuth\",", "\"httpQueryAuth\"": "{", "\"httpHeaderAuth\"": "{", "\"html\"": "\"<div class=\\\"form-group\\\" style=\\\"margin-bottom:16px;\\\">\\n <label class=\\\"form-label\\\" for=\\\"field-2\\\">\\n Enter research breadth (Default 2)\\n </label>\\n <p style=\\\"font-size:12px;color:#666;text-align:left\\\">\\n This value determines how many sources to explore.\\n </p>\\n <input\\n class=\\\"form-input\\\"\\n type=\\\"range\\\"\\n id=\\\"field-2\\\"\\n name=\\\"field-2\\\"\\n value=\\\"2\\\"\\n step=\\\"1\\\"\\n max=\\\"5\\\"\\n min=\\\"1\\\"\\n list=\\\"breadth-markers\\\"\\n >\\n <datalist\\n id=\\\"breadth-markers\\\"\\n style=\\\"display: flex;\\n flex-direction: row;\\n justify-content: space-between;\\n writing-mode: horizontal-tb;\\n margin-top: -10px;\\n text-align: center;\\n font-size: 10px;\\n margin-left: 16px;\\n margin-right: 16px;\\\"\\n >\\n <option value=\\\"1\\\" label=\\\"1\\\"></option>\\n <option value=\\\"2\\\" label=\\\"2\\\"></option>\\n <option value=\\\"3\\\" label=\\\"3\\\"></option>\\n <option value=\\\"4\\\" label=\\\"4\\\"></option>\\n <option value=\\\"5\\\" label=\\\"5\\\"></option>\\n </datalist>\\n</div>\\n\\n\",", "\"ignore\"": "\"a,img,picture,svg,video,audio,iframe\"", "\"destinationKey\"": "\"markdown\"", "\"placeholder\"": "\"=\",", "\"requiredField\"": "true", "\"workflowInputs\"": "{", "\"color\"": "7,", "\"width\"": "180,", "\"height\"": "260,", "\"content\"": "\"\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n### UPDATE NOTION CREDENTIAL HERE!\"", "\"operation\"": "\"notEmpty\",", "\"completionTitle\"": "\"=Thank you for using DeepResearcher.\",", "\"completionMessage\"": "\"=You may now close this window.\"", "\"waitForSubWorkflow\"": "true", "\"workflowId\"": "{", "\"data\"": "\"={{ $json }}\",", "\"jobType\"": "\"deepresearch_learnings\",", "\"requestId\"": "\"={{ $('JobType Router').first().json.requestId }}\"", "\"schema\"": "[", "\"display\"": "true,", "\"removed\"": "false,", "\"required\"": "false,", "\"displayName\"": "\"data\",", "\"defaultMatch\"": "false,", "\"canBeUsedToMatch\"": "true", "\"mappingMode\"": "\"defineBelow\",", "\"matchingColumns\"": "[],", "\"attemptToConvertTypes\"": "false,", "\"convertFieldsToString\"": "true", "\"jsonBody\"": "\"={{\\n{\\n \\\"children\\\": $json.block\\n}\\n}}\",", "\"sendQuery\"": "true,", "\"specifyBody\"": "\"json\",", "\"queryParameters\"": "{", "\"dataToSave\"": "{", "\"key\"": "\"Request ID|rich_text\",", "\"rules\"": "{", "\"outputKey\"": "\"report\",", "\"conditions\"": "[", "\"version\"": "2,", "\"leftValue\"": "\"={{ $json }}\",", "\"caseSensitive\"": "true,", "\"typeValidation\"": "\"strict\"", "\"combinator\"": "\"and\",", "\"operator\"": "{", "\"rightValue\"": "\"\"", "\"renameOutput\"": "true", "\"singleValue\"": "true", "\"alwaysOutputData\"": "true", "\"title\"": "\"={{ $json.output.title }}\"", "\"resource\"": "\"databasePage\",", "\"databaseId\"": "{", "\"cachedResultUrl\"": "\"https://www.notion.so/19486dd60c0c80da9cb7eb1468ea9afd\",", "\"cachedResultName\"": "\"n8n DeepResearch\"", "\"propertiesUi\"": "{", "\"propertyValues\"": "[", "\"textContent\"": "\"={{ $('Set Variables').first().json.request_id }}\"", "\"statusValue\"": "\"Done\"", "\"notionApi\"": "{", "\"limit\"": "1,", "\"filters\"": "{", "\"condition\"": "\"equals\",", "\"richTextValue\"": "\"={{ $json.requestId.toString() }}\"", "\"matchType\"": "\"allFilters\",", "\"filterType\"": "\"manual\"", "\"pageId\"": "{", "\"date\"": "\"={{ $now.toISO() }}\"", "\"tables\"": "true", "\"markdown\"": "\"={{ $json.text }}\"", "\"modelName\"": "\"models/gemini-2.0-flash\"", "\"googlePalmApi\"": "{", "\"maxTries\"": "2,", "\"timeout\"": "\"={{ 1000 * 60 }}\"", "\"sendHeaders\"": "true,", "\"headerParameters\"": "{", "\"nodeCredentialType\"": "\"notion<PERSON><PERSON>\"", "\"retryOnFail\"": "true,", "\"waitBetweenTries\"": "3000", "\"multiselect\"": "true,", "\"fieldOptions\"": "{", "\"option\"": "\"=I understand higher depth and breath values I've selected may incur longer wait times and higher costs. I acknowledging this and wish to proceed with the research request.\"", "\"jsCode\"": "\"const urls = $('JobType Router').first().json.data.all_urls;\\nconst chunksize = 50;\\nconst splits = Math.max(1, Math.floor(urls.length/chunksize));\\n\\nconst blocks = Array(splits).fill(0)\\n .map((_, idx) => {\\n const block = urls\\n .slice(\\n idx * chunksize, \\n (idx * chunksize) + chunksize - 1\\n )\\n .map(url => {\\n return {\\n object: \\\"block\\\",\\n type: \\\"bulleted_list_item\\\",\\n bulleted_list_item: {\\n rich_text: [\\n { type: \\\"text\\\", text: { content: url } }\\n ]\\n }\\n }\\n });\\n return { json: { block } }\\n });\\n\\nreturn [\\n { json: {\\n block:[{\\n \\\"object\\\": \\\"block\\\",\\n \\\"type\\\": \\\"heading_2\\\",\\n \\\"heading_2\\\": {\\n \\\"rich_text\\\": [\\n {\\n \\\"type\\\": \\\"text\\\",\\n \\\"text\\\": {\\n \\\"content\\\": \\\"Sources\\\"\\n }\\n }\\n ]\\n }\\n }]\\n } },\\n ...blocks\\n];\"", "\"aggregate\"": "\"aggregateAllItemData\"", "\"pinData\"": "{},", "\"connections\"": "{", "\"Item Ref\"": "{", "\"main\"": "[", "\"node\"": "\"Report Page Generator\",", "\"index\"": "0", "\"Create Row\"": "{", "\"Web Search\"": "{", "\"Valid Pages\"": "{", "\"Confirmation\"": "{", "\"Has Content?\"": "{", "\"Has Results?\"": "{", "\"Valid Blocks\"": "{", "\"Append Blocks\"": "{", "\"HTML to Array\"": "{", "\"Page Contents\"": "{", "\"SERP to Items\"": "{", "\"Set Variables\"": "{", "\"Tags to Items\"": "{", "\"URLs to Items\"": "{", "\"Empty Response\"": "{", "\"Execution Data\"": "{", "\"JobType Router\"": "{", "\"Convert to HTML\"": "{", "\"Set In-Progress\"": "{", "\"Get Existing Row\"": "{", "\"Research Request\"": "{", "\"Results to Items\"": "{", "\"Set Next Queries\"": "{", "\"Feedback to Items\"": "{", "\"For Each Block...\"": "{", "\"For Each Query...\"": "{", "\"Get Existing Row1\"": "{", "\"Get Initial Query\"": "{", "\"Is Depth Reached?\"": "{", "\"OpenAI Chat Model\"": "{", "\"ai_languageModel\"": "[", "\"Parse JSON blocks\"": "{", "\"Set Initial Query\"": "{", "\"Accumulate Results\"": "{", "\"Generate Learnings\"": "{", "\"On form submission\"": "{", "\"OpenAI Chat Model1\"": "{", "\"OpenAI Chat Model2\"": "{", "\"OpenAI Chat Model3\"": "{", "\"OpenAI Chat Model4\"": "{", "\"Convert to Markdown\"": "{", "\"DeepResearch Report\"": "{", "\"Clarifying Questions\"": "{", "\"DeepResearch Results\"": "{", "\"For Each Question...\"": "{", "\"Get Research Results\"": "{", "\"URL Sources to Lists\"": "{", "\"Ask Clarity Questions\"": "{", "\"Generate SERP Queries\"": "{", "\"Initiate DeepResearch\"": "{", "\"Report Page Generator\"": "{", "\"Top 5 Organic Results\"": "{", "\"Upload to Notion Page\"": "{", "\"DeepResearch Learnings\"": "{", "\"Notion Block Generator\"": "{", "\"DeepResearch Subworkflow\"": "{", "\"Google Gemini Chat Model\"": "{", "\"Structured Output Parser\"": "{", "\"ai_outputParser\"": "[", "\"Research Goal + Learnings\"": "{", "\"Structured Output Parser1\"": "{", "\"Structured Output Parser2\"": "{", "\"Structured Output Parser4\"": "{"}