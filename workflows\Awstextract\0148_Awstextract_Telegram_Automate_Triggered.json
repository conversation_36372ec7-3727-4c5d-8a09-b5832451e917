{"nodes": [{"name": "AWS Textract", "type": "n8n-nodes-base.awsTextract", "position": [700, 340], "parameters": {}, "credentials": {"aws": {"id": "9", "name": "aws"}}, "typeVersion": 1}, {"name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "position": [520, 220], "webhookId": "12345", "parameters": {"updates": ["*"], "additionalFields": {"download": true, "imageSize": "medium"}}, "credentials": {"telegramApi": {"id": "49", "name": "Telegram mybot"}}, "typeVersion": 1}, {"name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [880, 340], "parameters": {"table": "receipts", "options": {}, "operation": "append", "application": "qwertz", "addAllFields": false}, "credentials": {"airtableApi": {"id": "6", "name": "airtable_nodeqa"}}, "typeVersion": 1}, {"name": "AWS S3", "type": "n8n-nodes-base.awsS3", "position": [700, 100], "parameters": {"fileName": "={{$binary.data.fileName}}", "operation": "upload", "bucketName": "textract-demodata", "additionalFields": {}}, "credentials": {"aws": {"id": "9", "name": "aws"}}, "typeVersion": 1}], "connections": {"AWS Textract": {"main": [[{"node": "Airtable", "type": "main", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "AWS S3", "type": "main", "index": 0}, {"node": "AWS Textract", "type": "main", "index": 0}]]}}}