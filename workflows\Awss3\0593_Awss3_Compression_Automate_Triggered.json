{"meta": {"instanceId": "9e331a89ae45a204c6dee51c77131d32a8c962ec20ccf002135ea60bd285dba9"}, "nodes": [{"id": "5dbcd30b-7f84-4932-9dff-b5e9865f9b07", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [860, 680], "parameters": {}, "typeVersion": 1}, {"id": "639dd225-ae36-4d2b-b341-8662ffe39836", "name": "List ALL Files*", "type": "n8n-nodes-base.awsS3", "position": [1080, 680], "parameters": {"options": {"folderKey": "=yourFolder"}, "operation": "getAll", "returnAll": true, "bucketName": "=yourBucket"}, "typeVersion": 2}, {"id": "cb8b4b07-af86-45b0-9621-a02c22107741", "name": "Download ALL Files from Folder*", "type": "n8n-nodes-base.awsS3", "position": [1300, 680], "parameters": {"fileKey": "={{ $json.Key }}", "bucketName": "=yourBucket"}, "typeVersion": 2}, {"id": "df2a3f56-7656-427c-a3b1-df3f1f4997e9", "name": "All into one Item (include Binary)", "type": "n8n-nodes-base.aggregate", "position": [1520, 680], "parameters": {"options": {"includeBinaries": true}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "ca0085aa-77f0-4339-8821-11b8e53588da", "name": "Note3", "type": "n8n-nodes-base.stickyNote", "position": [420, 560], "parameters": {"width": 367.15098241985504, "height": 363.66522445338995, "content": "## Instructions\n\nThis workflow downloads all Files from a specific folder in a S3 Bucket and compresses them so you can download it via n8n or do further processings.\n\nFill in your **Credentials and Settings** in the Nodes marked with _\"*\"_.\n\n![Image](https://let-the-work-flow.com/logo-64.png)\nEnjoy the Workflow! ❤️ \nhttps://let-the-work-flow.com\nWorkflow Automation & Development"}, "typeVersion": 1}, {"id": "9b12152d-46b8-4e03-9a4b-5bbc0289c78c", "name": "Compress all of them to a ZIP", "type": "n8n-nodes-base.compression", "position": [1740, 680], "parameters": {"fileName": "=s3-export.zip", "operation": "compress", "binaryPropertyName": "={{ Object.keys($binary).join(',') }}"}, "typeVersion": 1.1}], "pinData": {}, "connections": {"List ALL Files*": {"main": [[{"node": "Download ALL Files from Folder*", "type": "main", "index": 0}]]}, "Download ALL Files from Folder*": {"main": [[{"node": "All into one Item (include Binary)", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "List ALL Files*", "type": "main", "index": 0}]]}, "All into one Item (include Binary)": {"main": [[{"node": "Compress all of them to a ZIP", "type": "main", "index": 0}]]}}}