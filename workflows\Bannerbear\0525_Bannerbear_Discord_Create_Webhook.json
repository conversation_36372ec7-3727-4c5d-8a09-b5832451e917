{"meta": {"instanceId": "26ba763460b97c249b82942b23b6384876dfeb9327513332e743c5f6219c2b8e"}, "nodes": [{"id": "81ea4c6a-d603-4688-8b72-d9c79faf7adf", "name": "n8n Form Trigger", "type": "n8n-nodes-base.formTrigger", "position": [1272, 455], "webhookId": "d280e773-3bd8-44ce-a147-8b404251fce9", "parameters": {"path": "d280e773-3bd8-44ce-a147-8b404251fce9", "options": {}, "formTitle": "<PERSON><PERSON><PERSON>", "formFields": {"values": [{"fieldType": "dropdown", "fieldLabel": "Template", "fieldOptions": {"values": [{"option": "n8n Meetup Template"}, {"option": "AI Meetup Template"}]}}, {"fieldType": "textarea", "fieldLabel": "Title of Event", "requiredField": true}, {"fieldType": "textarea", "fieldLabel": "Location of Event", "requiredField": true}, {"fieldType": "textarea", "fieldLabel": "Date of Event", "requiredField": true}, {"fieldType": "textarea", "fieldLabel": "Image Prompt", "requiredField": true}]}, "formDescription": "Generate an image and apply text"}, "typeVersion": 2}, {"id": "dea26687-4060-488b-a09f-e21900fec2fc", "name": "Upload to Cloudinary", "type": "n8n-nodes-base.httpRequest", "position": [1920, 480], "parameters": {"url": "https://api.cloudinary.com/v1_1/daglih2g8/image/upload", "method": "POST", "options": {}, "sendBody": true, "sendQuery": true, "contentType": "multipart-form-data", "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "file", "parameterType": "formBinaryData", "inputDataFieldName": "data"}]}, "genericAuthType": "httpQueryAuth", "queryParameters": {"parameters": [{"name": "upload_preset", "value": "n8n-workflows-preset"}]}}, "credentials": {"httpQueryAuth": {"id": "sT9jeKzZiLJ3bVPz", "name": "Cloudinary API"}}, "typeVersion": 4.2}, {"id": "4b73ba35-eac9-467b-b711-49061da30fbc", "name": "Send to Bannerbear Template", "type": "n8n-nodes-base.bannerbear", "position": [2260, 440], "parameters": {"templateId": "={{ $('Set Parameters').item.json.template_id }}", "modificationsUi": {"modificationsValues": [{"name": "placeholder_image", "text": "=", "imageUrl": "={{ $json.secure_url.replace('upload/','upload/f_auto,q_auto/') }}"}, {"name": "placeholder_text", "text": "={{ $('Set Parameters').item.json.title }}"}, {"name": "placeholder_location", "text": "={{ $('Set Parameters').item.json.location }}"}, {"name": "placeholder_date", "text": "={{ $('Set Parameters').item.json.date }}"}]}, "additionalFields": {"waitForImage": true, "waitForImageMaxTries": 10}}, "credentials": {"bannerbearApi": {"id": "jXg71GVWN3F4PvI8", "name": "Bannerbear account"}}, "typeVersion": 1}, {"id": "d9b8f63b-ee0f-40d6-9b1a-8213c7043b3a", "name": "Set Parameters", "type": "n8n-nodes-base.set", "position": [1452, 455], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8c526649-b8a8-4b9f-a805-41de053bb642", "name": "template_id", "type": "string", "value": "={{ {\n'AI Meetup Template': 'lzw71BD6VNLgD0eYkn',\n'n8n Meetup Template': 'n1MJGd52o696D7LaPV'\n}[$json.Template] ?? '' }}"}, {"id": "f5a3c285-719b-4a12-a669-47a63a880ac4", "name": "title", "type": "string", "value": "={{ $json[\"Title of Event\"] }}"}, {"id": "6713a88e-815c-416a-b838-b07006a090a3", "name": "location", "type": "string", "value": "={{ $json[\"Location of Event\"] }}"}, {"id": "3c331756-1f1f-4e27-b769-e3de860bfdf0", "name": "date", "type": "string", "value": "={{ $json[\"Date of Event\"] }}"}, {"id": "b933df30-8067-4a0a-bff1-64441490478d", "name": "image_prompt", "type": "string", "value": "={{ $json[\"Image Prompt\"] }}"}]}}, "typeVersion": 3.3}, {"id": "3290571f-e858-4b73-b27d-7077d4efad15", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1220, 280], "parameters": {"color": 7, "width": 392.4891967891814, "height": 357.1079372601395, "content": "## 1. Start with n8n Forms\n[Read more about using forms](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.formtrigger/)\n\nFor this demo, we'll use the form trigger for simple data capture but you could use webhooks for better customisation and/or integration into other workflows."}, "typeVersion": 1}, {"id": "560a6c43-07bd-4a5c-8af7-0cda78f345d4", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1640, 215.68990043281633], "parameters": {"color": 7, "width": 456.99271465116215, "height": 475.77059293291677, "content": "## 2. Use AI to Generate an Image\n[Read more about using OpenAI](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-langchain.openai)\n\nGenerating AI images is just as easy as generating text thanks for n8n's OpenAI node. Once completed, OpenAI will return a binary image file. We'll have to store this image externally however since we can't upload it directly BannerBear. I've chosen to use Cloudinary CDN but S3 is also a good choice."}, "typeVersion": 1}, {"id": "0ffe2ada-9cb6-4d4c-9d15-df83d5a596ce", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2120, 168.04517481270597], "parameters": {"color": 7, "width": 387.4250119152741, "height": 467.21699325771294, "content": "## 3. Create Social Media Banners with BannerBear.com\n[Read more about the BannerBear Node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.bannerbear)\n\nNow with your generated AI image and template variables, we're ready to send them to BannerBear which will use a predefined template to create our social media banner.\n"}, "typeVersion": 1}, {"id": "e8269a57-caab-40c6-bf47-95b64eccde81", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [2540, 299.6729638445606], "parameters": {"color": 7, "width": 404.9582850950252, "height": 356.8876009810222, "content": "## 4. Post directly to Social Media\n[Read more about using the Discord Node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.discord)\n\nWe'll share our event banner with our community in Discord. You can also choose to post this on your favourite social media channels."}, "typeVersion": 1}, {"id": "457a0744-4c08-4489-af50-5a746fa4b756", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [2120, 40], "parameters": {"color": 5, "width": 388.96199194175017, "height": 122.12691731521146, "content": "### 🙋‍♂️ Optimise your images!\nAI generated images can get quite large (20mb+) which may hit filesize limits for some services. I've used Cloudinary's optimise API to reduce the file size before sending to BannerBear."}, "typeVersion": 1}, {"id": "c38cc2c6-a595-48c8-a5be-668fd609c76b", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [2960, 220], "parameters": {"color": 5, "width": 391.9308945140308, "height": 288.0739771936459, "content": "### Result!\nHere is a screenshot of the generated banner.\n![Result](https://res.cloudinary.com/daglih2g8/image/upload/f_auto,q_auto,w_360/v1/n8n-workflows/qlzyrjjhxeh3zgerglti)"}, "typeVersion": 1}, {"id": "29ce299d-3444-4e71-b83c-edbe867e833f", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [800, 240], "parameters": {"width": 392.9673182916798, "height": 404.96428251481916, "content": "## Try It Out!\n### This workflow does the following:\n* Uses an n8n form to capture an event to be announced.\n* Form includes imagery required for the event and this is sent to OpenAI Dalle-3 service to generate.\n* Event details as well as the ai-generated image is then sent to the BannerBear.com service where a template is used.\n* The final event poster is created and posted to X (formerly Twitter)\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!"}, "typeVersion": 1}, {"id": "c01d1ac0-5ebe-4ef1-bece-d6ad8bbff94e", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [2200, 400], "parameters": {"width": 221.3032167915293, "height": 368.5789698912447, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n🚨**Required**\n* You'll need to create a template in BannerBear.\n* Once you have, map the template variables to fields in this node!"}, "typeVersion": 1}, {"id": "c929d9c4-1e18-4806-9fc6-fb3bf0fa75ad", "name": "Download Banner", "type": "n8n-nodes-base.httpRequest", "position": [2600, 480], "parameters": {"url": "={{ $json.image_url_jpg }}", "options": {}}, "typeVersion": 4.2}, {"id": "79d19004-7d82-42be-89d5-dcb3af5e3fb1", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [1857.0197380966872, 440], "parameters": {"width": 224.2834786948422, "height": 368.5789698912447, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n🚨**Required**\n* You'll need to change all ids and references to your own Cloudinary instance.\n* Feel free to change this to another service!"}, "typeVersion": 1}, {"id": "18ccd15f-65b6-46eb-8235-7fe19b13649d", "name": "Discord", "type": "n8n-nodes-base.discord", "position": [2780, 480], "parameters": {"files": {"values": [{}]}, "content": "=📅 New Event Alert!  {{ $('Set Parameters').item.json.title }} being held at  {{ $('Set Parameters').item.json.location }} on the  {{ $('Set Parameters').item.json.date }}! Don't miss it!", "guildId": {"__rl": true, "mode": "list", "value": "1248678443432808509", "cachedResultUrl": "https://discord.com/channels/1248678443432808509", "cachedResultName": "Datamoldxyz"}, "options": {}, "resource": "message", "channelId": {"__rl": true, "mode": "list", "value": "1248678443432808512", "cachedResultUrl": "https://discord.com/channels/1248678443432808509/1248678443432808512", "cachedResultName": "general"}}, "credentials": {"discordBotApi": {"id": "YUwD52E3oHsSUWdW", "name": "Discord Bot account"}}, "typeVersion": 2}, {"id": "7122fac9-4b4d-4fcf-a188-21af025a7fa8", "name": "Generate AI Banner Image", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1700, 480], "parameters": {"prompt": "={{ $json.image_prompt }}", "options": {"size": "1024x1024", "quality": "standard"}, "resource": "image"}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.3}], "pinData": {}, "connections": {"Set Parameters": {"main": [[{"node": "Generate AI Banner Image", "type": "main", "index": 0}]]}, "Download Banner": {"main": [[{"node": "Discord", "type": "main", "index": 0}]]}, "n8n Form Trigger": {"main": [[{"node": "Set Parameters", "type": "main", "index": 0}]]}, "Upload to Cloudinary": {"main": [[{"node": "Send to Bannerbear Template", "type": "main", "index": 0}]]}, "Generate AI Banner Image": {"main": [[{"node": "Upload to Cloudinary", "type": "main", "index": 0}]]}, "Send to Bannerbear Template": {"main": [[{"node": "Download Banner", "type": "main", "index": 0}]]}}}