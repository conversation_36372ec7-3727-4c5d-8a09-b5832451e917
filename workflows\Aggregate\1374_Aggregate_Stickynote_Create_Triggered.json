{"meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7"}, "nodes": [{"id": "087ae6e2-b333-4a30-9010-c78050203961", "name": "OpenAI Assistant", "type": "@n8n/n8n-nodes-langchain.openAiAssistant", "position": [1340, 460], "parameters": {"text": "=## Our Previous Conversation:\n{{ $json[\"messages\"].map(m => `\nHuman: ${m.human}\nAI Assistant: ${m.ai}\n`) }}\n## Current message:\n{{ $('Chat Trigger').item.json.chatInput }}", "options": {}, "assistantId": "asst_HDSAnzsp4WqY4UC1iI9auH5z"}, "credentials": {"openAiApi": {"id": "VQtv7frm7eLiEDnd", "name": "OpenAi account 7"}}, "typeVersion": 1}, {"id": "3793b10a-ebb7-42ec-8b9b-7fa3a353d9a3", "name": "Calculator", "type": "@n8n/n8n-nodes-langchain.toolCalculator", "position": [1500, 640], "parameters": {}, "typeVersion": 1}, {"id": "7bee2882-bb9e-402e-ba42-9b1ed0e1264b", "name": "Chat Memory Manager", "type": "@n8n/n8n-nodes-langchain.memoryManager", "position": [760, 460], "parameters": {}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "5c66e482-819e-47e7-90be-779e92364e2a", "name": "Chat Memory Manager1", "type": "@n8n/n8n-nodes-langchain.memoryManager", "position": [1720, 460], "parameters": {"mode": "insert", "messages": {"messageValues": [{"type": "user", "message": "={{ $('Chat Trigger').item.json.chatInput }}"}, {"type": "ai", "message": "={{ $json.output }}"}]}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "b96bf629-bd21-4528-8988-e63c5af89fd7", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [1140, 460], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "messages"}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "95001be1-f046-47e3-a58c-25bff170ba06", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [2320, 460], "parameters": {"fields": {"values": [{"name": "output", "stringValue": "={{ $('OpenAI Assistant').item.json.output }}"}]}, "options": {}}, "typeVersion": 3.2}, {"id": "4ea04793-c7fb-4b81-abf7-49590aa76ca7", "name": "Limit", "type": "n8n-nodes-base.limit", "position": [2100, 460], "parameters": {}, "typeVersion": 1}, {"id": "16921f74-d420-445a-9e09-19a6116a3267", "name": "<PERSON><PERSON>", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [460, 460], "webhookId": "1f83e8ac-d465-454a-8327-cef7f0149cb1", "parameters": {"public": true, "options": {"loadPreviousSession": "memory"}}, "typeVersion": 1}, {"id": "c0826494-779a-4c2d-93c9-746150ac9482", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [740, 400], "parameters": {"color": 7, "width": 514.8706020514577, "height": 196.64941360686112, "content": "Read contents of the chat from memory"}, "typeVersion": 1}, {"id": "4ce4594d-070a-4985-9c5d-fcd4ebc4a627", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1320, 400], "parameters": {"color": 7, "width": 298.02823821086326, "height": 196.64941360686112, "content": "Call the assistant, passing in the previous chat messages"}, "typeVersion": 1}, {"id": "49885b3b-de77-4c02-a35e-d188fee38831", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1700, 400], "parameters": {"color": 7, "width": 298.02823821086326, "height": 196.64941360686112, "content": "Add the latest chat messages to the memory"}, "typeVersion": 1}, {"id": "f45e8589-d61b-440a-ae89-31ded2738ef7", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [2080, 400], "parameters": {"color": 7, "width": 356.0564764217267, "height": 196.64941360686112, "content": "Return the model output"}, "typeVersion": 1}, {"id": "3b72a676-aaa2-472a-b055-1fed03f52101", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [360, 640], "parameters": {"height": 300.48941882630095, "content": "## Try me out\n1. In the OpenAI Assistant node, make sure your OpenAI credentials are set and choose an assistant to use (you'll need to create one if you don't have one already)\n2. Click the 'Chat' button below\n\n - In the first message, tell the AI what your name is\n - In a second message, ask the AI what your name is"}, "typeVersion": 1}, {"id": "a2250328-e4ce-4ac6-b4fe-658ab173bc28", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [1280, 880], "parameters": {"sessionKey": "={{ $('Chat Trigger').item.json.sessionId }}123", "contextWindowLength": 20}, "typeVersion": 1.1}], "pinData": {}, "connections": {"Limit": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "OpenAI Assistant", "type": "main", "index": 0}]]}, "Calculator": {"ai_tool": [[{"node": "OpenAI Assistant", "type": "ai_tool", "index": 0}]]}, "Chat Trigger": {"main": [[{"node": "Chat Memory Manager", "type": "main", "index": 0}]]}, "OpenAI Assistant": {"main": [[{"node": "Chat Memory Manager1", "type": "main", "index": 0}]]}, "Chat Memory Manager": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Chat Memory Manager1": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "<PERSON><PERSON>", "type": "ai_memory", "index": 0}, {"node": "Chat Memory Manager", "type": "ai_memory", "index": 0}, {"node": "Chat Memory Manager1", "type": "ai_memory", "index": 0}]]}}}