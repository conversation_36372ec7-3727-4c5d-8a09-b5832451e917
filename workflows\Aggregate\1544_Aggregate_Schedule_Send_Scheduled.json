{"id": "M8oLW9Qd59zNJzg2", "meta": {"instanceId": "1abe0e4c2be794795d12bf72aa530a426a6f87aabad209ed6619bcaf0f666fb0", "templateCredsSetupCompleted": true}, "name": "Email Summary Agent", "tags": [{"id": "G1v7CnFpOHsReVhM", "name": "Product", "createdAt": "2025-01-13T17:04:34.969Z", "updatedAt": "2025-01-13T17:04:34.969Z"}, {"id": "RagrXIh5iBDseqvj", "name": "AI", "createdAt": "2025-01-09T09:18:12.756Z", "updatedAt": "2025-01-09T09:18:12.756Z"}, {"id": "Yg2lfYteJZAoIeaC", "name": "Building blocks", "createdAt": "2025-01-13T17:05:49.788Z", "updatedAt": "2025-01-13T17:05:49.788Z"}, {"id": "ZuS1C3NpE8uBlFq4", "name": "Finance", "createdAt": "2025-01-13T17:05:03.996Z", "updatedAt": "2025-01-13T17:05:03.996Z"}, {"id": "aqlZb2qfWiaT4Xr5", "name": "IT Ops", "createdAt": "2025-01-03T12:20:11.917Z", "updatedAt": "2025-01-03T12:20:11.917Z"}, {"id": "fX8hRnEv4D8sLSzF", "name": "OpenAI", "createdAt": "2025-01-09T09:18:12.757Z", "updatedAt": "2025-01-09T09:18:12.757Z"}, {"id": "j1qBXzFADkR3sHSa", "name": "Marketing", "createdAt": "2025-01-13T17:03:54.468Z", "updatedAt": "2025-01-13T17:03:54.468Z"}, {"id": "x3OVvOuZkLx1hYpW", "name": "Support", "createdAt": "2025-01-13T17:05:40.900Z", "updatedAt": "2025-01-13T17:05:40.900Z"}, {"id": "xBOhq1kP3lza5ajE", "name": "HR", "createdAt": "2025-01-13T17:04:57.045Z", "updatedAt": "2025-01-13T17:04:57.045Z"}, {"id": "yy04JQqCaXepPdSa", "name": "Project Management", "createdAt": "2024-10-30T18:27:57.309Z", "updatedAt": "2024-10-30T18:27:57.309Z"}, {"id": "zJaZorWWcGpTp35U", "name": "DevOps", "createdAt": "2025-01-03T12:19:34.273Z", "updatedAt": "2025-01-03T12:19:34.273Z"}], "nodes": [{"id": "94c09c05-539b-452e-83b7-0a029bbe6b7f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-120, -140], "parameters": {"width": 248.47086922498647, "height": 314.47468983163634, "content": "- Starts the workflow every day at 7 AM.\n- Adjust the time if you want the workflow to run at a different hour."}, "typeVersion": 1}, {"id": "5e5cbc87-5c01-438b-a1c0-e8468d3ee20b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [160, -137.04548301590512], "parameters": {"width": 213.36643278764896, "height": 313.40934714314244, "content": "Fetches all emails received in the past 24 hours from the email address"}, "typeVersion": 1}, {"id": "9a82f5e9-7d0b-430f-9dbb-d8ae0b129dad", "name": "Daily 7AM Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-40, 0], "parameters": {"rule": {"interval": [{"triggerAtHour": 7}]}}, "typeVersion": 1.2}, {"id": "dd3e4b10-187b-45ce-b999-f0143e5af134", "name": "Fetch Emails - Past 24 Hours", "type": "n8n-nodes-base.gmail", "position": [220, 0], "webhookId": "20f1d11d-8a69-43f3-9323-33eaf1b3b600", "parameters": {"filters": {"q": "={{ \n  (() => {\n    const yesterday = new Date();\n    yesterday.setDate(yesterday.getDate() - 1);\n    return `<EMAIL> after:${yesterday.getFullYear()}/${(yesterday.getMonth() + 1).toString().padStart(2, '0')}/${yesterday.getDate().toString().padStart(2, '0')}`;\n  })()\n}}"}, "operation": "getAll", "returnAll": true}, "credentials": {"gmailOAuth2": {"id": "YFARhQXJAjbwXjSO", "name": "<PERSON><PERSON><PERSON>"}}, "typeVersion": 2.1}, {"id": "4a8fdfd9-93d7-43a2-92b0-88d845f217bf", "name": "Organize Email Data - Morning", "type": "n8n-nodes-base.aggregate", "position": [460, 0], "parameters": {"include": "specifiedFields", "options": {}, "aggregate": "aggregateAllItemData", "fieldsToInclude": "id, From, To, CC, snippet"}, "typeVersion": 1}, {"id": "9e2426e8-57ba-4708-b66f-b58bd19eabff", "name": "Summarize Emails with OpenAI - Morning", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [680, 0], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"content": "=Go through this email summary and identify all key details mentioned, any specific issues to look at, and action items.\nUse this format to output\n{\n  \"summary_of_emails\": [\n    \"Point 1\",\n    \"Point 2\",\n    \"Point 3\"\n  ],\n  \"actions\": [\n    {\n      \"name\": \"Name 1\",\n      \"action\": \"Action 1\"\n    },\n    {\n      \"name\": \"Name 1\",\n      \"action\": \"Action 2\"\n    },\n    {\n      \"name\": \"Name 2\",\n      \"action\": \"Action 3\"\n    }\n  ]\n}\n\nInput Data:\n\n {{ $json.data.toJsonString() }}\n\n"}]}, "jsonOutput": true}, "credentials": {"openAiApi": {"id": "ksU2WMcMqe2lPgRw", "name": "OpenAi account"}}, "typeVersion": 1.7}, {"id": "4aa68ee8-d38f-418a-9f20-6cc76850c638", "name": "Send Summary - Morning", "type": "n8n-nodes-base.gmail", "position": [1040, 0], "webhookId": "83f2aeb9-7b6c-4336-b5ed-8acfcd259850", "parameters": {"sendTo": "<EMAIL>", "message": "=<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Email Summary - <EMAIL></title>\n    <style>\n        body {\n            font-family: Arial, sans-serif;\n            margin: 0;\n            padding: 0;\n            background-color: #f9f9f9;\n            color: #333;\n            line-height: 1.6;\n        }\n        .email-container {\n            max-width: 600px;\n            margin: 20px auto;\n            background: #ffffff;\n            border: 1px solid #ddd;\n            border-radius: 10px;\n            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\n        }\n        .email-header {\n            background-color: #0073e6;\n            color: #fff;\n            padding: 20px;\n            text-align: center;\n            border-top-left-radius: 10px;\n            border-top-right-radius: 10px;\n        }\n        .email-header h1 {\n            margin: 0;\n            font-size: 24px;\n        }\n        .email-content {\n            padding: 20px;\n        }\n        .section-title {\n            font-size: 20px;\n            color: #0073e6;\n            margin-bottom: 10px;\n        }\n        ul {\n            list-style: none;\n            padding: 0;\n        }\n        ul li {\n            margin: 10px 0;\n            padding: 10px;\n            background: #f4f4f4;\n            border-left: 4px solid #0073e6;\n            border-radius: 5px;\n        }\n        .action-item {\n            font-weight: bold;\n            margin: 5px 0;\n        }\n        .action-detail {\n            margin-left: 10px;\n        }\n        .email-footer {\n            background-color: #0073e6;\n            color: #fff;\n            text-align: center;\n            padding: 10px;\n            font-size: 14px;\n            border-bottom-left-radius: 10px;\n            border-bottom-right-radius: 10px;\n        }\n    </style>\n</head>\n<body>\n    <div class=\"email-container\">\n        <div class=\"email-header\">\n            <h1>Email Summary</h1>\n        </div>\n        <div class=\"email-content\">\n            <div>\n                <h2 class=\"section-title\">Summary of Emails:</h2>\n                <ul>\n                    {{ $json.message.content.summary_of_emails.map(email => `<li>${email}</li>`).join('') }}\n                </ul>\n            </div>\n            <div>\n                <h2 class=\"section-title\">Actions:</h2>\n                <ul>\n                    {{ $json.message.content.actions.map(action => `\n                        <li>\n                            <span class=\"action-item\">${action.name}:</span>\n                            <span class=\"action-detail\">${action.action}</span>\n                        </li>\n                    `).join('') }}\n                </ul>\n            </div>\n        </div>\n        <div class=\"email-footer\">\n            <p>Generated by Quantana ESAgent <br /> A Quantana AI Labs Initiative\n        </div>\n    </div>\n</body>\n</html>", "options": {"ccList": "<EMAIL>", "appendAttribution": false, "replyToSenderOnly": false}, "subject": "=ESAgent - {{ new Date(new Date().setDate(new Date().getDate() - 1)).toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' }) }}-00:00 to {{ new Date(new Date().setDate(new Date().getDate())).toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' }) }}-07:00AM"}, "credentials": {"gmailOAuth2": {"id": "YFARhQXJAjbwXjSO", "name": "<PERSON><PERSON><PERSON>"}}, "typeVersion": 2.1}, {"id": "c7667667-9533-40cb-9c09-914a11560600", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [400, -132.6641804468672], "parameters": {"width": 226.7095107678671, "height": 305.83657700487913, "content": "Organizes the fetched email data, extracting fields like sender, receiver, CC, and a preview snippet."}, "typeVersion": 1}, {"id": "43955af4-3a18-44d7-8c8d-cf8051b18bdd", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [980, -180], "parameters": {"width": 232.8435827211592, "height": 359.7308639651144, "content": "- Sends the summarized email report to recipients with a styled HTML layout.\n- Update the \"sendTo\" and \"ccList\" fields with the email addresses of your recipients.\n\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"timezone": "Asia/Kolkata", "callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1"}, "versionId": "b18912ed-6c1f-4912-b75a-1553f7620917", "connections": {"Daily 7AM Trigger": {"main": [[{"node": "Fetch Emails - Past 24 Hours", "type": "main", "index": 0}]]}, "Fetch Emails - Past 24 Hours": {"main": [[{"node": "Organize Email Data - Morning", "type": "main", "index": 0}]]}, "Organize Email Data - Morning": {"main": [[{"node": "Summarize Emails with OpenAI - Morning", "type": "main", "index": 0}]]}, "Summarize Emails with OpenAI - Morning": {"main": [[{"node": "Send Summary - Morning", "type": "main", "index": 0}]]}}}