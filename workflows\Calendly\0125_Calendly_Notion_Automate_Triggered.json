{"nodes": [{"name": "Notion", "type": "n8n-nodes-base.notion", "position": [850, 400], "parameters": {"resource": "databasePage", "databaseId": "", "propertiesUi": {"propertyValues": [{"key": "Date|date", "range": true, "dateEnd": "={{$node[\"Function\"].json[\"payload\"][\"event\"][\"end_time\"]}}", "dateStart": "={{$node[\"Function\"].json[\"payload\"][\"event\"][\"invitee_start_time\"]}}"}, {"key": "email|email", "emailValue": "={{$json[\"email\"][0][\"email\"]}}"}, {"key": "Leads|name", "title": "={{$json[\"full_name\"]}}"}, {"key": "LinkedIn Profile|url", "urlValue": "={{$json[\"linkedin\"]}}"}, {"key": "Person|people", "peopleValue": ["22ad678a-175a-405c-b504-978d7804ebb8"]}, {"key": "Website|url", "urlValue": "={{$json[\"website\"]}}"}, {"key": "LinkedIn Company|url", "urlValue": "={{$json[\"company_linkedin\"]}}"}, {"key": "Civility|rich_text", "textContent": "={{$json[\"civility\"]}}"}]}}, "credentials": {"notionApi": {"id": "", "name": ""}}, "typeVersion": 1}, {"name": "Dropcontact", "type": "n8n-nodes-base.dropcontact", "position": [650, 400], "parameters": {"email": "={{$json[\"payload\"][\"invitee\"][\"email\"]}}", "options": {"siren": true, "language": "fr"}, "additionalFields": {"full_name": "={{$json[\"payload\"][\"invitee\"][\"name\"]}}", "last_name": "={{$json[\"payload\"][\"invitee\"][\"last_name\"]}}", "first_name": "={{$json[\"payload\"][\"invitee\"][\"first_name\"]}}"}}, "credentials": {"dropcontactApi": {"id": "", "name": ""}}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.calendlyTrigger", "position": [460, 400], "webhookId": "", "parameters": {"events": ["invitee.created"]}, "typeVersion": 1}], "connections": {"Dropcontact": {"main": [[{"node": "Notion", "type": "main", "index": 0}]]}, "Calendly Trigger": {"main": [[{"node": "Dropcontact", "type": "main", "index": 0}]]}}}