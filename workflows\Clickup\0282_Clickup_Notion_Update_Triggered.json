{"meta": {"instanceId": "237600ca44303ce91fa31ee72babcdc8493f55ee2c0e8aa2b78b3b4ce6f70bd9"}, "nodes": [{"id": "22e8e117-2475-4b06-966c-9b35c9c749f8", "name": "On updated database page", "type": "n8n-nodes-base.notionTrigger", "position": [180, 620], "parameters": {"event": "pagedUpdatedInDatabase", "pollTimes": {"item": [{"mode": "everyMinute"}]}, "databaseId": "38aa89c7-defd-4268-be2d-9119590521a9"}, "credentials": {"notionApi": {"id": "9", "name": "[UPDATE ME]"}}, "typeVersion": 1}, {"id": "6938eddf-39ec-46c4-a9a9-082ee0edd836", "name": "Update an existing task", "type": "n8n-nodes-base.clickUp", "position": [400, 620], "parameters": {"id": "={{$node[\"On updated database page\"].json[\"ClickUp ID\"]}}", "operation": "update", "updateFields": {"name": "={{$node[\"On updated database page\"].json[\"Task name\"]}}", "status": "={{$node[\"On updated database page\"].json[\"Status\"]}}", "dueDate": "={{$node[\"On updated database page\"].json[\"Deadline\"][\"start\"]}}"}}, "credentials": {"clickUpApi": {"id": "29", "name": "[UPDATE ME]"}}, "typeVersion": 1}, {"id": "84cd269a-e732-408e-8b1a-66b1a7623fc1", "name": "On task status updated", "type": "n8n-nodes-base.clickUpTrigger", "position": [180, 820], "webhookId": "86d6bbce-1591-4db9-9ccb-214ab0977ae8", "parameters": {"team": "2627397", "events": ["taskStatusUpdated"], "filters": {}}, "credentials": {"clickUpApi": {"id": "29", "name": "[UPDATE ME]"}}, "typeVersion": 1}, {"id": "a5d6cee8-9dae-45ca-9540-4835365a4ab1", "name": "Get database page by ClickUp ID", "type": "n8n-nodes-base.notion", "position": [400, 820], "parameters": {"filters": {"conditions": [{"key": "ClickUp ID|rich_text", "condition": "equals", "richTextValue": "={{$node[\"On task status updated\"].json[\"task_id\"]}}"}]}, "options": {}, "resource": "databasePage", "operation": "getAll", "returnAll": true, "databaseId": "38aa89c7-defd-4268-be2d-9119590521a9", "filterType": "manual"}, "credentials": {"notionApi": {"id": "9", "name": "[UPDATE ME]"}}, "typeVersion": 2}, {"id": "eeaff75d-8c47-4e2d-b2e2-87d5b6e59499", "name": "Update the status of found database page", "type": "n8n-nodes-base.notion", "position": [620, 820], "parameters": {"pageId": "={{$node[\"Get database page by ClickUp ID\"].json[\"id\"]}}", "resource": "databasePage", "operation": "update", "propertiesUi": {"propertyValues": [{"key": "Status|select", "selectValue": "={{$node[\"On task status updated\"].json[\"history_items\"][0][\"after\"][\"status\"]}}"}]}}, "credentials": {"notionApi": {"id": "9", "name": "[UPDATE ME]"}}, "typeVersion": 2}], "connections": {"On task status updated": {"main": [[{"node": "Get database page by ClickUp ID", "type": "main", "index": 0}]]}, "On updated database page": {"main": [[{"node": "Update an existing task", "type": "main", "index": 0}]]}, "Get database page by ClickUp ID": {"main": [[{"node": "Update the status of found database page", "type": "main", "index": 0}]]}}}