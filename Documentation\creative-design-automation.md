# Creative Design Automation - N8N Workflows

## Overview
This document catalogs the **Creative Design Automation** workflows from the n8n Community Workflows repository.

**Category:** Creative Design Automation  
**Total Workflows:** 23  
**Generated:** 2025-07-27  
**Source:** https://scan-might-updates-postage.trycloudflare.com/api

---

## Workflows

### Manual Webflow Automate Triggered
**Filename:** `0022_Manual_Webflow_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Webflow for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Webflow,  

---

### Manual Editimage Create Webhook
**Filename:** `0137_Manual_Editimage_Create_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Editimage, and Itemlists to create new records. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (12 nodes)  
**Integrations:** Httprequest,Editimage,Itemlists,  

---

### Add text to an image downloaded from the internet
**Filename:** `0343_Manual_Editimage_Create_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Editimage for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Httprequest,Editimage,  

---

### Bannerbear Discord Create Webhook
**Filename:** `0525_Bannerbear_Discord_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Discord, OpenAI, and Bannerbear to create new records. Uses 16 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** Discord,OpenAI,Bannerbear,Httprequest,Form Trigger,  

---

### Editimage Manual Update Webhook
**Filename:** `0575_Editimage_Manual_Update_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Editimage, and Google Drive to update existing data. Uses 13 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** Lmchatgooglegemini,Editimage,Google Drive,Outputparserstructured,Httprequest,Chainllm,  

---

### Code Editimage Update Webhook
**Filename:** `0577_Code_Editimage_Update_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Editimage, and Outputparserstructured to update existing data. Uses 16 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** Lmchatgooglegemini,Editimage,Outputparserstructured,Httprequest,Chainllm,Cal.com,  

---

### Splitout Editimage Update Triggered
**Filename:** `0579_Splitout_Editimage_Update_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Editimage, and Google Drive to update existing data. Uses 11 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** Lmchatgooglegemini,Editimage,Google Drive,Splitout,Outputparserstructured,Chainllm,  

---

### Code Editimage Import Webhook
**Filename:** `0580_Code_Editimage_Import_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Editimage, and Google Drive for data processing. Uses 20 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (20 nodes)  
**Integrations:** Lmchatgooglegemini,Editimage,Google Drive,Compression,Informationextractor,Httprequest,Chainllm,  

---

### Code Editimage Update Webhook
**Filename:** `0598_Code_Editimage_Update_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Cal.com, and Editimage to update existing data. Uses 16 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** high (16 nodes)  
**Integrations:** Httprequest,Cal.com,Editimage,  

---

### Code Editimage Update Webhook
**Filename:** `0665_Code_Editimage_Update_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Cal.com, and Editimage to update existing data. Uses 14 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Httprequest,Cal.com,Editimage,Box,  

---

### Receive updates when a form submission occurs in your Webflow website
**Filename:** `0953_Webflow_Update_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Webflow to update existing data. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Webflow,  

---

### Manual Bannerbear Automate Triggered
**Filename:** `1012_Manual_Bannerbear_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Bannerbear for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Bannerbear,  

---

### Manual Bannerbear Automate Triggered
**Filename:** `1013_Manual_Bannerbear_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Bannerbear for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Bannerbear,  

---

### Manual Editimage Update Webhook
**Filename:** `1040_Manual_Editimage_Update_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Editimage to update existing data. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Httprequest,Editimage,  

---

### Splitout Editimage Automate Triggered
**Filename:** `1329_Splitout_Editimage_Automate_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Editimage, and Google Drive for data processing. Uses 11 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** Lmchatgooglegemini,Editimage,Google Drive,Splitout,Outputparserstructured,Chainllm,  

---

### Remove Advanced Background from Google Drive Images
**Filename:** `1343_Splitout_Editimage_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Editimage, and Google Drive for data processing. Uses 16 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** Splitinbatches,Editimage,Google Drive,Splitout,Httprequest,  

---

### Editimage Manual Automation Webhook
**Filename:** `1369_Editimage_Manual_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Editimage, and Google Drive for data processing. Uses 13 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** Lmchatgooglegemini,Editimage,Google Drive,Outputparserstructured,Httprequest,Chainllm,  

---

### Manual Editimage Create Webhook
**Filename:** `1393_Manual_Editimage_Create_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Editimage, and Itemlists to create new records. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (12 nodes)  
**Integrations:** Httprequest,Editimage,Itemlists,  

---

### Code Editimage Automation Webhook
**Filename:** `1423_Code_Editimage_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Editimage, and Outputparserstructured for data processing. Uses 16 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** Lmchatgooglegemini,Editimage,Outputparserstructured,Httprequest,Chainllm,Cal.com,  

---

### Code Editimage Automation Webhook
**Filename:** `1605_Code_Editimage_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Cal.com, and Editimage for data processing. Uses 14 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Httprequest,Cal.com,Editimage,Box,  

---

### Bannerbear Discord Automation Webhook
**Filename:** `1665_Bannerbear_Discord_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Discord, OpenAI, and Bannerbear for data processing. Uses 16 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** Discord,OpenAI,Bannerbear,Httprequest,Form Trigger,  

---

### Code Editimage Automation Webhook
**Filename:** `1699_Code_Editimage_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Editimage, and Google Drive for data processing. Uses 20 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (20 nodes)  
**Integrations:** Lmchatgooglegemini,Editimage,Google Drive,Compression,Informationextractor,Httprequest,Chainllm,  

---

### Remove Advanced Background from Google Drive Images
**Filename:** `1943_Splitout_Editimage_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Editimage, and Google Drive for data processing. Uses 16 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** Splitinbatches,Editimage,Google Drive,Splitout,Httprequest,  

---


## Summary

**Total Creative Design Automation workflows:** 23  
**Documentation generated:** 2025-07-27 14:34:50  
**API Source:** https://scan-might-updates-postage.trycloudflare.com/api  

This documentation was automatically generated using the n8n workflow API endpoints.
