{"meta": {"instanceId": "257476b1ef58bf3cb6a46e65fac7ee34a53a5e1a8492d5c6e4da5f87c9b82833", "templateId": "2129"}, "nodes": [{"id": "02cd5c16-de39-4e5c-acf8-fd3287662dfb", "name": "if company does not exist on CRM", "type": "n8n-nodes-base.if", "position": [2240, 140], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "19bf6d06-76f4-479a-a9d8-2157414190b3", "operator": {"type": "object", "operation": "empty", "singleValue": true}, "leftValue": "={{ $input.item.json }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "c1325687-53cf-404f-9f9c-16696be0fcce", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [380, 240], "parameters": {"width": 257.64008049230523, "height": 255.97404402400312, "content": "## Setup\n1. Add `Clearbit`, `<PERSON><PERSON><PERSON>`, and `<PERSON><PERSON><PERSON>` credentials\n2. <PERSON>lick on `Test workflow`\n3. Book meeting on Calendly so the event starts the workflow"}, "typeVersion": 1}, {"id": "8c1f4364-1e5b-4d63-b11d-295a683ace73", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [660, 240], "parameters": {"color": 4, "width": 225.41119920533646, "height": 260.**************, "content": "Replace this node with your booking tool of choice"}, "typeVersion": 1}, {"id": "0fd0557e-56da-4b64-8f50-e931022d630b", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2340, 40], "parameters": {"color": 4, "width": 219.1588560076235, "height": 260.**************, "content": "Map all data found about the company that you interested in"}, "typeVersion": 1}, {"id": "9fe1367e-1d8b-4384-a920-8a6ebfcbb0db", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1040, 240], "parameters": {"color": 4, "width": 233.**************, "height": 260.**************, "content": "Make sure to map the email field from the data your booking tool provides"}, "typeVersion": 1}, {"id": "241835fc-1369-4c67-8de2-ffc86336369f", "name": "Enrich company", "type": "n8n-nodes-base.clearbit", "notes": "Enrich company", "position": [1680, 140], "parameters": {"domain": "={{ $json.employment.domain }}", "additionalFields": {}}, "credentials": {"clearbitApi": {"id": "cKDImrinp9tg0ZHW", "name": "Clearbit account"}}, "notesInFlow": false, "typeVersion": 1}, {"id": "ba694bd1-0b7a-4caa-b31a-cbde1d77e626", "name": "Create company", "type": "n8n-nodes-base.hubspot", "position": [2520, 120], "parameters": {"name": "={{ $('Enrich company').item.json.name }}", "resource": "company", "authentication": "oAuth2", "additionalFields": {"twitterBio": "={{ $('Enrich company').item.json.twitter.bio }}", "description": "={{ $('Enrich company').item.json.description }}", "yearFounded": "={{ $('Enrich company').item.json.foundedYear }}", "countryRegion": "={{ $('Enrich company').item.json.geo.country }}", "twitterHandle": "={{ $('Enrich company').item.json.twitter.handle }}", "totalMoneyRaised": "={{ $('Enrich company').item.json.metrics.raised }}", "twitterFollowers": "={{ $('Enrich company').item.json.twitter.followers }}", "companyDomainName": "={{ $('Enrich company').item.json.domain }}", "numberOfEmployees": "={{ $('Enrich company').item.json.metrics.employees }}"}}, "credentials": {"hubspotOAuth2Api": {"id": "WEONgGVHLYPjIE6k", "name": "HubSpot account"}}, "typeVersion": 2, "alwaysOutputData": true}, {"id": "b3ce17b4-ea85-4051-9231-67218d8586ea", "name": "Upsert contact", "type": "n8n-nodes-base.hubspot", "position": [2780, 120], "parameters": {"email": "={{ $('Enrich email').item.json.email }}", "options": {"resolveData": true}, "authentication": "oAuth2", "additionalFields": {"associatedCompanyId": "={{ $json.companyId }}"}}, "credentials": {"hubspotOAuth2Api": {"id": "WEONgGVHLYPjIE6k", "name": "HubSpot account"}}, "typeVersion": 2}, {"id": "************************************", "name": "Update company", "type": "n8n-nodes-base.hubspot", "position": [2520, 420], "parameters": {"resource": "company", "companyId": {"__rl": true, "mode": "id", "value": "={{ $json.companyId }}"}, "operation": "update", "updateFields": {"twitterBio": "={{ $('Enrich company').item.json.twitter.bio }}", "description": "={{ $('Enrich company').item.json.description }}", "countryRegion": "={{ $('Enrich company').item.json.geo.country }}", "twitterHandle": "={{ $('Enrich company').item.json.twitter.handle }}", "totalMoneyRaised": "={{ $('Enrich company').item.json.metrics.raised }}", "twitterFollowers": "={{ $('Enrich company').item.json.twitter.followers }}", "numberOfEmployees": "={{ $('Enrich company').item.json.metrics.employees }}"}, "authentication": "oAuth2"}, "credentials": {"hubspotOAuth2Api": {"id": "WEONgGVHLYPjIE6k", "name": "HubSpot account"}}, "typeVersion": 2}, {"id": "************************************", "name": "Contact not found, do nothing", "type": "n8n-nodes-base.noOp", "position": [1380, 600], "parameters": {}, "typeVersion": 1}, {"id": "97bcd33e-333b-4e2f-a450-e415c774e1b1", "name": "Enrich email", "type": "n8n-nodes-base.clearbit", "notes": "Enrich email", "onError": "continueErrorOutput", "position": [1100, 340], "parameters": {"email": "={{ $json.payload.email }}", "resource": "person", "additionalFields": {}}, "credentials": {"clearbitApi": {"id": "cKDImrinp9tg0ZHW", "name": "Clearbit account"}}, "notesInFlow": false, "typeVersion": 1}, {"id": "1a759054-07ea-44eb-bfa5-d487630f84d0", "name": "Filter out personal emails", "type": "n8n-nodes-base.filter", "position": [920, 340], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "df6da257-7ec4-4433-9d29-2f12f6f11944", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.payload.email }}", "rightValue": "@gmail.com"}, {"id": "6a66410c-a2e8-494b-b972-751116e49418", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.payload.email }}", "rightValue": "@yahoo.com"}, {"id": "378fbe41-0e37-4756-93ca-bf81bfe8b258", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.payload.email }}", "rightValue": "@outlook.com"}, {"id": "fd05b842-3c11-4e1a-9226-0b0fd359ccab", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.payload.email }}", "rightValue": "@hotmail.com"}, {"id": "6040ea5d-3c15-4513-915b-47a55c24e8a7", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.payload.email }}", "rightValue": "@icloud.com"}, {"id": "ce67ed8b-34f9-4ba2-83d4-cc04cea090bb", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.payload.email }}", "rightValue": "@mail.com"}, {"id": "92c043ae-72de-41d8-887b-9e94755a9060", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.payload.email }}", "rightValue": "@aol.com"}, {"id": "377bcc07-e5a1-4e3a-a4da-4446f316a0b2", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.payload.email }}", "rightValue": "@zoho.com"}, {"id": "c09c7057-2833-4085-8cb9-d2f28d853724", "operator": {"type": "string", "operation": "notContains"}, "leftValue": "={{ $json.payload.email }}", "rightValue": "@gmx"}]}}, "typeVersion": 2}, {"id": "17905d5e-bdc6-4419-b10e-5f390b92f269", "name": "Search company", "type": "n8n-nodes-base.hubspot", "position": [1980, 140], "parameters": {"limit": 1, "domain": "={{ $json.domain }}", "options": {}, "resource": "company", "operation": "searchByDomain", "authentication": "oAuth2"}, "credentials": {"hubspotOAuth2Api": {"id": "WEONgGVHLYPjIE6k", "name": "HubSpot account"}}, "typeVersion": 2, "alwaysOutputData": true}, {"id": "c3eff32b-a767-4165-9424-112cb85c8949", "name": "Upsert lead", "type": "n8n-nodes-base.hubspot", "position": [1680, 440], "parameters": {"email": "={{ $('Enrich email').item.json.email }}", "options": {}, "authentication": "oAuth2", "additionalFields": {"lastName": "={{ $('Enrich email').item.json.name.familyName }}", "firstName": "={{ $('Enrich email').item.json.name.fullName }}"}}, "credentials": {"hubspotOAuth2Api": {"id": "WEONgGVHLYPjIE6k", "name": "HubSpot account"}}, "typeVersion": 2}, {"id": "************************************", "name": "If person has a company", "type": "n8n-nodes-base.if", "position": [1380, 340], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1a7aad55-5f4c-4bbc-a098-90f00a29be85", "operator": {"type": "string", "operation": "notEquals"}, "leftValue": "={{ $json.employment.domain }}", "rightValue": "={{ null }}"}]}}, "typeVersion": 2}, {"id": "372c6118-5670-4afb-8e1d-df61c24acfd3", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.calendlyTrigger", "position": [720, 340], "webhookId": "9690577f-aa08-427e-9338-798c719361b1", "parameters": {"events": ["invitee.created"]}, "credentials": {"calendlyApi": {"id": "MJZuKpbZfBDXlvaH", "name": "Calendly account"}}, "typeVersion": 1}], "pinData": {}, "connections": {"Enrich email": {"main": [[{"node": "If person has a company", "type": "main", "index": 0}], [{"node": "Contact not found, do nothing", "type": "main", "index": 0}]]}, "Create company": {"main": [[{"node": "Upsert contact", "type": "main", "index": 0}]]}, "Enrich company": {"main": [[{"node": "Search company", "type": "main", "index": 0}]]}, "Search company": {"main": [[{"node": "if company does not exist on CRM", "type": "main", "index": 0}]]}, "Calendly Trigger": {"main": [[{"node": "Filter out personal emails", "type": "main", "index": 0}]]}, "If person has a company": {"main": [[{"node": "Enrich company", "type": "main", "index": 0}], [{"node": "Upsert lead", "type": "main", "index": 0}]]}, "Filter out personal emails": {"main": [[{"node": "Enrich email", "type": "main", "index": 0}]]}, "if company does not exist on CRM": {"main": [[{"node": "Create company", "type": "main", "index": 0}], [{"node": "Update company", "type": "main", "index": 0}]]}}}