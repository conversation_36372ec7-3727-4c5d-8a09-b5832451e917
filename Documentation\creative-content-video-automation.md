# Creative Content & Video Automation - N8N Workflows

## Overview
This document catalogs the **Creative Content & Video Automation** workflows from the n8n Community Workflows repository.

**Category:** Creative Content & Video Automation  
**Total Workflows:** 35  
**Generated:** 2025-07-27  
**Source:** https://scan-might-updates-postage.trycloudflare.com/api

---

## Workflows

### Manual Googleslides Automate Triggered
**Filename:** `0016_Manual_Googleslides_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Googleslides for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Googleslides,  

---

### Get all the stories starting with `release` and publish them
**Filename:** `0046_Manual_Storyblok_Import_Triggered.json`  
**Description:** Manual workflow that integrates with Storyblok for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Storyblok,  

---

### Create, update, and get an entry in Strapi
**Filename:** `0079_Manual_Strapi_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Strapi to create new records. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Strapi,  

---

### Googleslides Slack Automate Triggered
**Filename:** `0095_Googleslides_Slack_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Airtable, Hubspot, and Slack for data processing. Uses 10 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Airtable,Hubspot,Slack,Googleslides,  

---

### Strapi Webhook Automation Webhook
**Filename:** `0183_Strapi_Webhook_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Twitter/X, Webhook, and Strapi for data processing. Uses 14 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Twitter/X,Webhook,Strapi,Googlecloudnaturallanguage,Form Trigger,Interval,  

---

### Youtube Telegram Send Scheduled
**Filename:** `0197_Youtube_Telegram_Send_Scheduled.json`  
**Description:** Manual workflow that orchestrates Interval, Telegram, and Youtube for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Interval,Telegram,Youtube,  

---

### Create, update, and get a post in Ghost
**Filename:** `0217_Manual_Ghost_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Ghost to create new records. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Ghost,  

---

### Wordpress-to-csv
**Filename:** `0359_Manual_Wordpress_Automation_Triggered.json`  
**Description:** Manual workflow that orchestrates Wordpress, Spreadsheetfile, and Writebinaryfile for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Wordpress,Spreadsheetfile,Writebinaryfile,  

---

### Schedule Spotify Create Scheduled
**Filename:** `0382_Schedule_Spotify_Create_Scheduled.json`  
**Description:** Scheduled automation that integrates with Spotify to create new records. Uses 11 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (11 nodes)  
**Integrations:** Spotify,  

---

### Upload video, create playlist and add video to playlist
**Filename:** `0476_Manual_Youtube_Create_Triggered.json`  
**Description:** Manual workflow that connects Youtube and Readbinaryfile to create new records. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Youtube,Readbinaryfile,  

---

### Manual Youtube Create Triggered
**Filename:** `0477_Manual_Youtube_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Youtube to create new records. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (9 nodes)  
**Integrations:** Youtube,  

---

### Wordpress Filter Update Scheduled
**Filename:** `0502_Wordpress_Filter_Update_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Wordpress, Airtable, and Markdown to update existing data. Uses 13 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** Wordpress,Airtable,Markdown,Httprequest,  

---

### Strapi Splitout Create Webhook
**Filename:** `0584_Strapi_Splitout_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, OpenAI, and Google Drive to create new records. Uses 36 nodes and integrates with 12 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (36 nodes)  
**Integrations:** Splitinbatches,OpenAI,Google Drive,Splitout,Google Sheets,Strapi,Wordpress,Httprequest,Webflow,Chainllm,Form Trigger,Executeworkflow,  

---

### Schedule Wordpress Automate Scheduled
**Filename:** `0631_Schedule_Wordpress_Automate_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Wordpress, Zoom, and Slack for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (4 nodes)  
**Integrations:** Wordpress,Zoom,Slack,  

---

### Wordpress Converttofile Process Triggered
**Filename:** `0721_Wordpress_Converttofile_Process_Triggered.json`  
**Description:** Manual workflow that orchestrates Wordpress, Converttofile, and Google Drive for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (7 nodes)  
**Integrations:** Wordpress,Converttofile,Google Drive,  

---

### Form Youtube Update Triggered
**Filename:** `0732_Form_Youtube_Update_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Agent, and Outputparserstructured to update existing data. Uses 11 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** OpenAI,Agent,Outputparserstructured,Form Trigger,Youtube,  

---

### DSP Certificate w/ Google Forms
**Filename:** `0754_Googleslides_Noop_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Google Drive, Google Sheets, and Gmail for data processing. Uses 17 nodes and integrates with 5 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Google Drive,Google Sheets,Gmail,Server-Sent Events,Googleslides,  

---

### Manual Wordpress Create Webhook
**Filename:** `0757_Manual_Wordpress_Create_Webhook.json`  
**Description:** Manual workflow that orchestrates Wordpress, Chainllm, and OpenAI to create new records. Uses 10 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (10 nodes)  
**Integrations:** Wordpress,Chainllm,OpenAI,Httprequest,  

---

### Code Ghost Create Triggered
**Filename:** `0844_Code_Ghost_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, OpenAI, and Google Sheets to create new records. Uses 14 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Splitinbatches,OpenAI,Google Sheets,Agent,LinkedIn,Ghost,  

---

### Manual Wordpress Automate Triggered
**Filename:** `1014_Manual_Wordpress_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Wordpress for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Wordpress,  

---

### Create a post and update the post in WordPress
**Filename:** `1075_Manual_Wordpress_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Wordpress to create new records. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Wordpress,  

---

### Manual Contentful Automation Triggered
**Filename:** `1086_Manual_Contentful_Automation_Triggered.json`  
**Description:** Manual workflow that integrates with Contentful for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Contentful,  

---

### Publish post to a publication
**Filename:** `1139_Manual_Medium_Automation_Triggered.json`  
**Description:** Manual workflow that integrates with Medium for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Medium,  

---

### Sample Spotify
**Filename:** `1181_Manual_Spotify_Automation_Triggered.json`  
**Description:** Manual workflow that integrates with Spotify for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Spotify,  

---

### Auto categorize wordpress template
**Filename:** `1322_Manual_Wordpress_Automation_Triggered.json`  
**Description:** Manual workflow that orchestrates Wordpress, Agent, and OpenAI for data processing. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (9 nodes)  
**Integrations:** Wordpress,Agent,OpenAI,  

---

### Automate Content Generator for WordPress with DeepSeek R1
**Filename:** `1327_Wordpress_Manual_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Wordpress, Google Sheets, and OpenAI for data processing. Uses 17 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Wordpress,Google Sheets,OpenAI,Httprequest,  

---

### Strapi Webhook Automate Webhook
**Filename:** `1336_Strapi_Webhook_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Twitter/X, Webhook, and Strapi for data processing. Uses 14 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Twitter/X,Webhook,Strapi,Googlecloudnaturallanguage,Form Trigger,Interval,  

---

### Strapi Splitout Automation Webhook
**Filename:** `1434_Strapi_Splitout_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, OpenAI, and Google Drive for data processing. Uses 36 nodes and integrates with 12 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (36 nodes)  
**Integrations:** Splitinbatches,OpenAI,Google Drive,Splitout,Google Sheets,Strapi,Wordpress,Httprequest,Webflow,Chainllm,Form Trigger,Executeworkflow,  

---

### The Ultimate Guide to Optimize WordPress Blog Posts with AI
**Filename:** `1550_Wordpress_Manual_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Lmchatopenrouter, and Google Sheets for data processing. Uses 21 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (21 nodes)  
**Integrations:** OpenAI,Lmchatopenrouter,Google Sheets,Outputparserstructured,Wordpress,Httprequest,Chainllm,  

---

### Post New YouTube Videos to X
**Filename:** `1574_Schedule_Youtube_Create_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Youtube, Twitter/X, and OpenAI for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (6 nodes)  
**Integrations:** Youtube,Twitter/X,OpenAI,  

---

### Post New YouTube Videos to X
**Filename:** `1602_Schedule_Youtube_Create_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Youtube, Twitter/X, and OpenAI for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (6 nodes)  
**Integrations:** Youtube,Twitter/X,OpenAI,  

---

### Auto categorize wordpress template
**Filename:** `1826_Manual_Wordpress_Automation_Triggered.json`  
**Description:** Manual workflow that orchestrates Wordpress, Agent, and OpenAI for data processing. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (9 nodes)  
**Integrations:** Wordpress,Agent,OpenAI,  

---

### 📄🛠️PDF2Blog
**Filename:** `1837_Code_Ghost_Automation_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatopenai, Agent, and Extractfromfile for data processing. Uses 12 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Lmchatopenai,Agent,Extractfromfile,Outputparserstructured,Form Trigger,Ghost,  

---

### Create Custom Presentations per Lead
**Filename:** `1845_Googleslides_Extractfromfile_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Form Trigger, Google Sheets, and Google Drive to create new records. Uses 14 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Form Trigger,Google Sheets,Google Drive,Googleslides,  

---

### Automate Content Generator for WordPress with DeepSeek R1
**Filename:** `1949_Wordpress_Manual_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Wordpress, Google Sheets, and OpenAI for data processing. Uses 17 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Wordpress,Google Sheets,OpenAI,Httprequest,  

---


## Summary

**Total Creative Content & Video Automation workflows:** 35  
**Documentation generated:** 2025-07-27 14:34:40  
**API Source:** https://scan-might-updates-postage.trycloudflare.com/api  

This documentation was automatically generated using the n8n workflow API endpoints.
