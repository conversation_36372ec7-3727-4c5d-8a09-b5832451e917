# Web Scraping & Data Extraction - N8N Workflows

## Overview
This document catalogs the **Web Scraping & Data Extraction** workflows from the n8n Community Workflows repository.

**Category:** Web Scraping & Data Extraction  
**Total Workflows:** 264  
**Generated:** 2025-07-27  
**Source:** https://scan-might-updates-postage.trycloudflare.com/api

---

## Workflows

### Openweathermap Cron Automate Scheduled
**Filename:** `0006_Openweathermap_Cron_Automate_Scheduled.json`  
**Description:** Scheduled automation that connects Plivo and Openweathermap for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (3 nodes)  
**Integrations:** Plivo,Openweathermap,  

---

### Send updates about the position of the ISS every minute to a topic in ActiveMQ
**Filename:** `0015_HTTP_Cron_Update_Webhook.json`  
**Description:** Scheduled automation that connects Httprequest and Amqp to update existing data. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,Amqp,  

---

### HTTP Awssqs Automation Scheduled
**Filename:** `0021_HTTP_Awssqs_Automation_Scheduled.json`  
**Description:** Scheduled automation that connects Httprequest and Awssqs for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,Awssqs,  

---

### HTTP Googlebigquery Automation Scheduled
**Filename:** `0023_HTTP_Googlebigquery_Automation_Scheduled.json`  
**Description:** Scheduled automation that connects Httprequest and Googlebigquery for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,Googlebigquery,  

---

### HTTP Mqtt Automation Webhook
**Filename:** `0033_HTTP_Mqtt_Automation_Webhook.json`  
**Description:** Scheduled automation that connects Httprequest and Mqtt for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,Mqtt,  

---

### Get a volume and add it to your bookshelf
**Filename:** `0037_Manual_Googlebooks_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Googlebooks for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Googlebooks,  

---

### HTTP Htmlextract Create Webhook
**Filename:** `0048_HTTP_Htmlextract_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Discord, Webhook, and Htmlextract to create new records. Uses 7 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Discord,Webhook,Htmlextract,Httprequest,Notion,  

---

### Send daily weather updates via a message in Line
**Filename:** `0065_Openweathermap_Line_Update_Scheduled.json`  
**Description:** Scheduled automation that connects Openweathermap and Line to update existing data. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (3 nodes)  
**Integrations:** Openweathermap,Line,  

---

### Send daily weather updates via a message using the Gotify node
**Filename:** `0072_Openweathermap_Cron_Update_Scheduled.json`  
**Description:** Scheduled automation that connects Openweathermap and Gotify to update existing data. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (3 nodes)  
**Integrations:** Openweathermap,Gotify,  

---

### Manual HTTP Monitor Webhook
**Filename:** `0074_Manual_HTTP_Monitor_Webhook.json`  
**Description:** Manual workflow that integrates with Httprequest for monitoring and reporting. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (8 nodes)  
**Integrations:** Httprequest,  

---

### Syncro Alert to OpsGenie
**Filename:** `0077_HTTP_Noop_Sync_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and Webhook to synchronize data. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Httprequest,Webhook,  

---

### Plex Automatic Throttler
**Filename:** `0083_Noop_HTTP_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Cal.com, and Webhook for data processing. Uses 21 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** high (21 nodes)  
**Integrations:** Httprequest,Cal.com,Webhook,  

---

### What To Eat
**Filename:** `0084_HTTP_Cron_Automation_Webhook.json`  
**Description:** Scheduled automation that connects Httprequest and Emailsend for data processing. Uses 9 nodes.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** medium (9 nodes)  
**Integrations:** Httprequest,Emailsend,  

---

### HTTP Github Create Scheduled
**Filename:** `0093_HTTP_GitHub_Create_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Httprequest, GitHub, and Form Trigger to create new records. Uses 11 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (11 nodes)  
**Integrations:** Httprequest,GitHub,Form Trigger,  

---

### Manual HTTP Create Webhook
**Filename:** `0102_Manual_HTTP_Create_Webhook.json`  
**Description:** Manual workflow that orchestrates Htmlextract, Emailsend, and Httprequest to create new records. Uses 8 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (8 nodes)  
**Integrations:** Htmlextract,Emailsend,Httprequest,Spreadsheetfile,Itemlists,  

---

### Receive updates for the position of the ISS every minute and push it to a database
**Filename:** `0136_HTTP_Googlefirebaserealtimedatabase_Update_Webhook.json`  
**Description:** Scheduled automation that connects Httprequest and Googlefirebaserealtimedatabase to update existing data. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,Googlefirebaserealtimedatabase,  

---

### HTTP Mysql Automation Webhook
**Filename:** `0139_HTTP_Mysql_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and MySQL for data processing. Uses 6 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,Webhook,MySQL,PostgreSQL,  

---

### HTTP Gitlab Automation Webhook
**Filename:** `0143_HTTP_Gitlab_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and GitLab for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Httprequest,GitLab,  

---

### HTTP Twitter Automation Scheduled
**Filename:** `0144_HTTP_Twitter_Automation_Scheduled.json`  
**Description:** Scheduled automation that connects Httprequest and Twitter/X for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,Twitter/X,  

---

### HTTP Dropbox Update Webhook
**Filename:** `0153_HTTP_Dropbox_Update_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Xml, and Dropbox to update existing data. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,Xml,Dropbox,  

---

### Mattermost Webhook
**Filename:** `0154_HTTP_Mattermost_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and Mattermost for data processing. Uses 3 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Httprequest,Webhook,Mattermost,  

---

### HTTP Awsrekognition Automation Webhook
**Filename:** `0156_HTTP_Awsrekognition_Automation_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Google Sheets, and Awsrekognition for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,Google Sheets,Awsrekognition,  

---

### Send daily weather updates via a push notification using Spontit
**Filename:** `0161_Openweathermap_Spontit_Update_Scheduled.json`  
**Description:** Scheduled automation that connects Spontit and Openweathermap to update existing data. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (3 nodes)  
**Integrations:** Spontit,Openweathermap,  

---

### HTTP Telegram Send Webhook
**Filename:** `0162_HTTP_Telegram_Send_Webhook.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Airtable, and Telegram for data processing. Uses 15 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (15 nodes)  
**Integrations:** Httprequest,Airtable,Telegram,  

---

### HTTP Slack Create Webhook
**Filename:** `0167_HTTP_Slack_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Hubspot, and Slack to create new records. Uses 5 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,Hubspot,Slack,Form Trigger,  

---

### Manual HTTP Automation Webhook
**Filename:** `0181_Manual_HTTP_Automation_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Airtable, and Functionitem for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,Airtable,Functionitem,  

---

### Receive the weather information of any city
**Filename:** `0196_Openweathermap_Webhook_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that connects Webhook and Openweathermap for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Webhook,Openweathermap,  

---

### Bubble Data Access
**Filename:** `0199_Manual_HTTP_Automation_Webhook.json`  
**Description:** Manual workflow that integrates with Httprequest for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Httprequest,  

---

### HTTP Googlesheets Automation Webhook
**Filename:** `0223_HTTP_GoogleSheets_Automation_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Spreadsheetfile, and Google Sheets for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,Spreadsheetfile,Google Sheets,  

---

### HTTP Googlesheets Send Webhook
**Filename:** `0224_HTTP_GoogleSheets_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Writebinaryfile, Google Sheets, and Gmail for data processing. Uses 14 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Writebinaryfile,Google Sheets,Gmail,Movebinarydata,Httprequest,Spreadsheetfile,  

---

### Manual HTTP Create Webhook
**Filename:** `0229_Manual_HTTP_Create_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Htmlextract, and Itemlists to create new records. Uses 14 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (14 nodes)  
**Integrations:** Httprequest,Htmlextract,Itemlists,  

---

### Get the logo, icon, and information of a company and store it in Airtable
**Filename:** `0242_Manual_Brandfetch_Import_Triggered.json`  
**Description:** Manual workflow that connects Airtable and Brandfetch for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Airtable,Brandfetch,  

---

### HTTP Stripe Create Webhook
**Filename:** `0245_HTTP_Stripe_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Stripe, and Pipedrive to create new records. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Httprequest,Stripe,Pipedrive,  

---

### HTTP Github Create Webhook
**Filename:** `0252_HTTP_GitHub_Create_Webhook.json`  
**Description:** Webhook-triggered automation that connects GitHub and Pipedrive to create new records. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** GitHub,Pipedrive,  

---

### HTTP Github Send Webhook
**Filename:** `0253_HTTP_GitHub_Send_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, GitHub, and Pipedrive for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,GitHub,Pipedrive,  

---

### Manual HTTP Create Webhook
**Filename:** `0259_Manual_HTTP_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Salesforce, Httprequest, and Spreadsheetfile to create new records. Uses 14 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Salesforce,Httprequest,Spreadsheetfile,Renamekeys,Itemlists,  

---

### Manual HTTP Create Webhook
**Filename:** `0271_Manual_HTTP_Create_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Xml, and Google Sheets to create new records. Uses 10 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (10 nodes)  
**Integrations:** Httprequest,Xml,Google Sheets,Itemlists,  

---

### Send updates about the position of the ISS every minute to a topic in RabbitMQ
**Filename:** `0287_HTTP_Rabbitmq_Update_Scheduled.json`  
**Description:** Scheduled automation that connects Httprequest and Rabbitmq to update existing data. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,Rabbitmq,  

---

### HTTP Respondtowebhook Import Webhook
**Filename:** `0306_HTTP_Respondtowebhook_Import_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and Respondtowebhook for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Httprequest,Webhook,Respondtowebhook,  

---

### HTTP Manual Automation Webhook
**Filename:** `0310_HTTP_Manual_Automation_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Spreadsheetfile, and Google Sheets for data processing. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (8 nodes)  
**Integrations:** Httprequest,Spreadsheetfile,Google Sheets,  

---

### Create, add an attachment, and send a draft using the Microsoft Outlook node
**Filename:** `0312_Manual_HTTP_Create_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Outlook to create new records. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,Outlook,  

---

### HTTP Schedule Create Scheduled
**Filename:** `0313_HTTP_Schedule_Create_Scheduled.json`  
**Description:** Scheduled automation that connects Discord and Google Calendar to create new records. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (9 nodes)  
**Integrations:** Discord,Google Calendar,  

---

### Create Nextcloud Deck card from email
**Filename:** `0344_HTTP_Emailreadimap_Create_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Email (IMAP) to create new records. Uses 3 nodes.  
**Status:** Active  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Httprequest,Email (IMAP),  

---

### Dialpad to Syncro
**Filename:** `0347_HTTP_GoogleSheets_Sync_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and Google Sheets to synchronize data. Uses 22 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** high (22 nodes)  
**Integrations:** Httprequest,Webhook,Google Sheets,  

---

### ImapEmail, XmlToJson, POST-HTTP-Request
**Filename:** `0350_HTTP_Emailreadimap_Send_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Email (IMAP), and Xml for data processing. Uses 5 nodes and integrates with 4 services.  
**Status:** Active  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,Email (IMAP),Xml,Movebinarydata,  

---

### Website check
**Filename:** `0358_HTTP_Discord_Monitor_Scheduled.json`  
**Description:** Scheduled automation that connects Httprequest and Discord for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,Discord,  

---

### xSend and check TTS (Text-to-speech) voice calls end email verification
**Filename:** `0362_Code_HTTP_Send_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Emailsend, and Form Trigger for data processing. Uses 19 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (19 nodes)  
**Integrations:** Httprequest,Emailsend,Form Trigger,  

---

### HTTP Executeworkflow Automate Webhook
**Filename:** `0363_HTTP_Executeworkflow_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, OpenAI, and Agent for data processing. Uses 20 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (20 nodes)  
**Integrations:** Markdown,OpenAI,Agent,Toolworkflow,Httprequest,Chat,Executeworkflow,  

---

### BillBot
**Filename:** `0364_HTTP_Twilio_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Telegram, and Google Sheets for data processing. Uses 6 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,Telegram,Google Sheets,Twilio,  

---

### HTTP Manual Create Webhook
**Filename:** `0384_HTTP_Manual_Create_Webhook.json`  
**Description:** Manual workflow that integrates with Httprequest to create new records. Uses 47 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** high (47 nodes)  
**Integrations:** Httprequest,  

---

### HTTP Manual Automation Webhook
**Filename:** `0390_HTTP_Manual_Automation_Webhook.json`  
**Description:** Manual workflow that orchestrates Splitinbatches, Html, and Httprequest for data processing. Uses 10 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (10 nodes)  
**Integrations:** Splitinbatches,Html,Httprequest,Itemlists,Medium,  

---

### HTTP Spreadsheetfile Create Webhook
**Filename:** `0394_HTTP_Spreadsheetfile_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Airtable, and Spreadsheetfile to create new records. Uses 17 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (17 nodes)  
**Integrations:** Httprequest,Airtable,Spreadsheetfile,  

---

### HTTP Executeworkflow Send Webhook
**Filename:** `0405_HTTP_Executeworkflow_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Agent, and Toolworkflow for data processing. Uses 18 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** OpenAI,Agent,Toolworkflow,Informationextractor,Chat,Executeworkflow,Cal.com,Memorybufferwindow,  

---

### Schedule HTTP Update Scheduled
**Filename:** `0412_Schedule_HTTP_Update_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Google Sheets, LinkedIn, and Form Trigger to update existing data. Uses 11 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (11 nodes)  
**Integrations:** Google Sheets,LinkedIn,Form Trigger,  

---

### Schedule HTTP Send Webhook
**Filename:** `0422_Schedule_HTTP_Send_Webhook.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Hubspot, and Gmail for data processing. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (12 nodes)  
**Integrations:** Httprequest,Hubspot,Gmail,  

---

### HTTP Stickynote Create Webhook
**Filename:** `0440_HTTP_Stickynote_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Notion, and Webhook to create new records. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Httprequest,Notion,Webhook,  

---

### HTTP Googlesheets Create Webhook
**Filename:** `0441_HTTP_GoogleSheets_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Webhook, Google Sheets, and Slack to create new records. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Webhook,Google Sheets,Slack,  

---

### Schedule HTTP Create Webhook
**Filename:** `0448_Schedule_HTTP_Create_Webhook.json`  
**Description:** Scheduled automation that connects Notion and N8N to create new records. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (10 nodes)  
**Integrations:** Notion,N8N,  

---

### HTTP Stickynote Create Webhook
**Filename:** `0450_HTTP_Stickynote_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and Microsoft Teams to create new records. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Httprequest,Webhook,Microsoft Teams,  

---

### HTTP Stickynote Create Webhook
**Filename:** `0463_HTTP_Stickynote_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and Highlevel to create new records. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Httprequest,Webhook,Highlevel,  

---

### HTTP Googlesheets Update Webhook
**Filename:** `0470_HTTP_GoogleSheets_Update_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Google Sheets, and Removeduplicates to update existing data. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,Google Sheets,Removeduplicates,  

---

### HTTP Form Create Webhook
**Filename:** `0471_HTTP_Form_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and Stripe to create new records. Uses 7 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Httprequest,Webhook,Stripe,Form Trigger,  

---

### Schedule HTTP Create Webhook
**Filename:** `0478_Schedule_HTTP_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Google Sheets, and Gmail to create new records. Uses 14 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Httprequest,Google Sheets,Gmail,Form Trigger,  

---

### HTTP Stickynote Create Webhook
**Filename:** `0485_HTTP_Stickynote_Create_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and Webhook to create new records. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Httprequest,Webhook,  

---

### HTTP Respondtowebhook Create Webhook
**Filename:** `0492_HTTP_Respondtowebhook_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and Respondtowebhook to create new records. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,Webhook,Respondtowebhook,  

---

### HTTP Keap Create Webhook
**Filename:** `0493_HTTP_Keap_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and Keap to create new records. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Httprequest,Webhook,Keap,  

---

### HTTP Htmlextract Send Webhook
**Filename:** `0494_HTTP_Htmlextract_Send_Webhook.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Htmlextract, and Emailsend for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (7 nodes)  
**Integrations:** Httprequest,Htmlextract,Emailsend,  

---

### Manual HTTP Update Webhook
**Filename:** `0495_Manual_HTTP_Update_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Airtable, and Html to update existing data. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,Airtable,Html,  

---

### HTTP Stickynote Create Webhook
**Filename:** `0505_HTTP_Stickynote_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Sendinblue, and Form Trigger to create new records. Uses 22 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (22 nodes)  
**Integrations:** Httprequest,Sendinblue,Form Trigger,  

---

### HTTP Schedule Automation Webhook
**Filename:** `0510_HTTP_Schedule_Automation_Webhook.json`  
**Description:** Scheduled automation that integrates with Httprequest for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,  

---

### Manual HTTP Automation Webhook
**Filename:** `0515_Manual_HTTP_Automation_Webhook.json`  
**Description:** Manual workflow that orchestrates Readwritefile, Httprequest, and Google Drive for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (7 nodes)  
**Integrations:** Readwritefile,Httprequest,Google Drive,  

---

### HTTP Stickynote Process Webhook
**Filename:** `0517_HTTP_Stickynote_Process_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Readwritefile for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,Readwritefile,  

---

### Manual HTTP Update Webhook
**Filename:** `0531_Manual_HTTP_Update_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Airtable, and Agent to update existing data. Uses 29 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (29 nodes)  
**Integrations:** OpenAI,Airtable,Agent,Toolworkflow,Outputparserstructured,Httprequest,Executeworkflow,  

---

### HTTP Filter Monitor Webhook
**Filename:** `0549_HTTP_Filter_Monitor_Webhook.json`  
**Description:** Manual workflow that connects Splitout and Stripe for monitoring and reporting. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (7 nodes)  
**Integrations:** Splitout,Stripe,  

---

### HTTP Slack Create Webhook
**Filename:** `0550_HTTP_Slack_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and Slack to create new records. Uses 11 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (11 nodes)  
**Integrations:** Httprequest,Webhook,Slack,  

---

### HTTP Stickynote Create Webhook
**Filename:** `0551_HTTP_Stickynote_Create_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Splitout to create new records. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (12 nodes)  
**Integrations:** Httprequest,Splitout,  

---

### HTTP Webhook Create Webhook
**Filename:** `0559_HTTP_Webhook_Create_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and Webhook to create new records. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,Webhook,  

---

### HTTP Stickynote Automate Webhook
**Filename:** `0566_HTTP_Stickynote_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that connects Executeworkflow and Httprequest for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Executeworkflow,Httprequest,  

---

### HTTP Schedule Create Webhook
**Filename:** `0588_HTTP_Schedule_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Cal.com, and Server-Sent Events to create new records. Uses 18 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (18 nodes)  
**Integrations:** Httprequest,Cal.com,Server-Sent Events,  

---

### HTTP Telegram Create Webhook
**Filename:** `0594_HTTP_Telegram_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Telegram, OpenAI, and Supabase to create new records. Uses 17 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (17 nodes)  
**Integrations:** Telegram,OpenAI,Supabase,  

---

### HTTP Respondtowebhook Create Webhook
**Filename:** `0606_HTTP_Respondtowebhook_Create_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and Webhook to create new records. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Httprequest,Webhook,  

---

### HTTP Filter Automation Scheduled
**Filename:** `0611_HTTP_Filter_Automation_Scheduled.json`  
**Description:** Scheduled automation that integrates with Httprequest for data processing. Uses 17 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** high (17 nodes)  
**Integrations:** Httprequest,  

---

### HTTP Stickynote Create Webhook
**Filename:** `0622_HTTP_Stickynote_Create_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and OpenAI to create new records. Uses 11 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (11 nodes)  
**Integrations:** Httprequest,OpenAI,  

---

### HTTP Schedule Send Scheduled
**Filename:** `0624_HTTP_Schedule_Send_Scheduled.json`  
**Description:** Scheduled automation that connects Httprequest and Splitinbatches for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (7 nodes)  
**Integrations:** Httprequest,Splitinbatches,  

---

### HTTP Schedule Create Scheduled
**Filename:** `0626_HTTP_Schedule_Create_Scheduled.json`  
**Description:** Scheduled automation that integrates with Httprequest to create new records. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,  

---

### HTTP Stickynote Create Webhook
**Filename:** `0636_HTTP_Stickynote_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Agent, and Chat to create new records. Uses 19 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** OpenAI,Agent,Chat,Form Trigger,Executeworkflow,Memorybufferwindow,Textclassifier,  

---

### HTTP Schedule Automation Webhook
**Filename:** `0637_HTTP_Schedule_Automation_Webhook.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Discord, and OpenAI for data processing. Uses 8 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (8 nodes)  
**Integrations:** Httprequest,Discord,OpenAI,Form Trigger,  

---

### HTTP Rssfeedread Create Webhook
**Filename:** `0641_HTTP_Rssfeedread_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Datetime, and Rssfeedread to create new records. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Httprequest,Datetime,Rssfeedread,  

---

### HTTP Extractfromfile Process Webhook
**Filename:** `0642_HTTP_Extractfromfile_Process_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Extractfromfile, and OpenAI for data processing. Uses 11 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (11 nodes)  
**Integrations:** Httprequest,Extractfromfile,OpenAI,  

---

### Splitout HTTP Send Webhook
**Filename:** `0680_Splitout_HTTP_Send_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Markdown, Lmchatgooglegemini, and Hackernews for data processing. Uses 10 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Markdown,Lmchatgooglegemini,Hackernews,Splitout,Emailsend,Httprequest,Chainllm,Form Trigger,  

---

### Aggregate HTTP Create Webhook
**Filename:** `0681_Aggregate_HTTP_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Airtable, and Agent to create new records. Uses 41 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (41 nodes)  
**Integrations:** OpenAI,Airtable,Agent,Toolworkflow,Httprequest,Chat,Executeworkflow,Toolcode,Memorybufferwindow,  

---

### HTTP Form Automation Webhook
**Filename:** `0687_HTTP_Form_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Lmchatopenai, and Chainsummarization for data processing. Uses 10 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Httprequest,Lmchatopenai,Chainsummarization,Form Trigger,  

---

### Transform Image to Lego Style Using Line and Dall-E
**Filename:** `0688_HTTP_Webhook_Process_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and OpenAI for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,Webhook,OpenAI,  

---

### HTTP Stripe Create Webhook
**Filename:** `0707_HTTP_Stripe_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Stripe, and Quickbooks to create new records. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Httprequest,Stripe,Quickbooks,  

---

### Code HTTP Create Webhook
**Filename:** `0709_Code_HTTP_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Twitter/X, OpenAI, and Google Sheets to create new records. Uses 19 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** Twitter/X,OpenAI,Google Sheets,LinkedIn,Emailsend,Outputparserstructured,Httprequest,Chainllm,  

---

### Manual HTTP Update Webhook
**Filename:** `0713_Manual_HTTP_Update_Webhook.json`  
**Description:** Manual workflow that integrates with Httprequest to update existing data. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Httprequest,  

---

### HTTP Schedule Create Scheduled
**Filename:** `0717_HTTP_Schedule_Create_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Splitinbatches, and Form Trigger to create new records. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (10 nodes)  
**Integrations:** Httprequest,Splitinbatches,Form Trigger,  

---

### Generate Image Workflow
**Filename:** `0734_Manual_HTTP_Create_Webhook.json`  
**Description:** Manual workflow that integrates with Httprequest for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Httprequest,  

---

### Streamline Your Zoom Meetings with Secure, Automated Stripe Payments
**Filename:** `0739_HTTP_Form_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Google Sheets, Gmail, and Stripe for data processing. Uses 20 nodes and integrates with 6 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (20 nodes)  
**Integrations:** Google Sheets,Gmail,Stripe,Httprequest,Form Trigger,Zoom,  

---

### Daily Text Affirmations
**Filename:** `0745_HTTP_Telegram_Automation_Webhook.json`  
**Description:** Scheduled automation that connects Httprequest and Telegram for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (3 nodes)  
**Integrations:** Httprequest,Telegram,  

---

### Telegram Weather Workflow
**Filename:** `0751_Openweathermap_Telegram_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects Telegram and Openweathermap for data processing. Uses 3 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Telegram,Openweathermap,  

---

### post to mattermost v2
**Filename:** `0752_HTTP_Rssfeedread_Automation_Scheduled.json`  
**Description:** Scheduled automation that connects Httprequest and Rssfeedread for data processing. Uses 6 nodes.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,Rssfeedread,  

---

### HTTP Telegram Create Webhook
**Filename:** `0771_HTTP_Telegram_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Telegram, and Agent to create new records. Uses 16 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** OpenAI,Telegram,Agent,Extractfromfile,Outputparserstructured,Httprequest,Toolserpapi,  

---

### N8N Español - NocodeBot
**Filename:** `0775_HTTP_Executecommand_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Telegram, and Executecommand for data processing. Uses 7 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Httprequest,Telegram,Executecommand,  

---

### HTTP Stickynote Import Webhook
**Filename:** `0778_HTTP_Stickynote_Import_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Google Drive for data processing. Uses 21 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** high (21 nodes)  
**Integrations:** Httprequest,Google Drive,  

---

### Manual HTTP Create Webhook
**Filename:** `0779_Manual_HTTP_Create_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Readwritefile to create new records. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Httprequest,Readwritefile,  

---

### Form Html Create Triggered
**Filename:** `0805_Form_Html_Create_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates OpenAI, Splitout, and Html to create new records. Uses 8 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** OpenAI,Splitout,Html,Emailsend,Form Trigger,  

---

### HTTP Manual Send Webhook
**Filename:** `0825_HTTP_Manual_Send_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and Executeworkflow for data processing. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Httprequest,Executeworkflow,  

---

### Splitout HTTP Send Webhook
**Filename:** `0840_Splitout_HTTP_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Splitout, and Agent for data processing. Uses 16 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** OpenAI,Splitout,Agent,Toolworkflow,Httprequest,Chat,Executeworkflow,Memorybufferwindow,  

---

### HTTP Schedule Update Webhook
**Filename:** `0870_HTTP_Schedule_Update_Webhook.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Telegram, and Form Trigger to update existing data. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (9 nodes)  
**Integrations:** Httprequest,Telegram,Form Trigger,  

---

### Wait HTTP Create Webhook
**Filename:** `0871_Wait_HTTP_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Sheets, and Gmail to create new records. Uses 17 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** OpenAI,Google Sheets,Gmail,Httprequest,Chainllm,Form Trigger,Cal.com,  

---

### HTTP Aggregate Import Webhook
**Filename:** `0878_HTTP_Aggregate_Import_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Executeworkflow, Mcp, and Toolworkflow for data processing. Uses 20 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (20 nodes)  
**Integrations:** Executeworkflow,Mcp,Toolworkflow,Httprequest,  

---

### Filter HTTP Update Webhook
**Filename:** `0879_Filter_HTTP_Update_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Toolworkflow, Httprequest, and Mcp to update existing data. Uses 25 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (25 nodes)  
**Integrations:** Toolworkflow,Httprequest,Mcp,Executeworkflow,Cal.com,  

---

### Googletasks HTTP Update Webhook
**Filename:** `0881_Googletasks_HTTP_Update_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, OpenAI, and Google Sheets to update existing data. Uses 17 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Splitinbatches,OpenAI,Google Sheets,Html,Httprequest,Google Tasks,  

---

### Converttofile HTTP Create Webhook
**Filename:** `0889_Converttofile_HTTP_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Converttofile, OpenAI, and Google Drive to create new records. Uses 18 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** Converttofile,OpenAI,Google Drive,Google Sheets,Httprequest,Chainllm,  

---

### AccountCraft WhatsApp Automation - Infridet
**Filename:** `0901_HTTP_GoogleSheets_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Webhook, Google Sheets, and Emailsend for data processing. Uses 6 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Webhook,Google Sheets,Emailsend,Httprequest,WhatsApp,  

---

### Automated PDF to HTML Conversion
**Filename:** `0934_HTTP_Code_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and Google Drive for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Httprequest,Google Drive,  

---

### Clockify to Syncro
**Filename:** `0935_HTTP_GoogleSheets_Sync_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and Google Sheets to synchronize data. Uses 13 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (13 nodes)  
**Integrations:** Httprequest,Webhook,Google Sheets,  

---

### Daily poems in Telegram
**Filename:** `0936_HTTP_Lingvanex_Automation_Webhook.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Telegram, and Lingvanex for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,Telegram,Lingvanex,  

---

### HTTP Editimage Update Webhook
**Filename:** `0937_HTTP_Editimage_Update_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and Editimage to update existing data. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Httprequest,Webhook,Editimage,  

---

### NameCheap Dynamic DNS (DDNS)
**Filename:** `0939_HTTP_Cron_Automation_Webhook.json`  
**Description:** Scheduled automation that integrates with Httprequest for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (7 nodes)  
**Integrations:** Httprequest,  

---

### HTTP Medium Automation Webhook
**Filename:** `0952_HTTP_Medium_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and Medium for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Httprequest,Webhook,Medium,  

---

### HTTP Automation Webhook
**Filename:** `0955_HTTP_Automation_Webhook.json`  
**Description:** Manual workflow that integrates with Httprequest for data processing. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (1 nodes)  
**Integrations:** Httprequest,  

---

### HTTP Readbinaryfile Automation Webhook
**Filename:** `0956_HTTP_Readbinaryfile_Automation_Webhook.json`  
**Description:** Manual workflow that connects Readbinaryfile and Httprequest for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Readbinaryfile,Httprequest,  

---

### Cocktail Recipe Sharing
**Filename:** `0964_HTTP_Bannerbear_Automation_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Bannerbear, and Rocket.Chat for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,Bannerbear,Rocket.Chat,  

---

### HTTP Discord Import Scheduled
**Filename:** `0966_HTTP_Discord_Import_Scheduled.json`  
**Description:** Scheduled automation that connects Httprequest and Discord for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,Discord,  

---

### Daily AI News Translation & Summary with GPT-4 and Telegram Delivery
**Filename:** `0970_HTTP_Schedule_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Lmchatopenai, and Agent for data processing. Uses 12 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Httprequest,Lmchatopenai,Agent,Telegram,  

---

### post to wallabag
**Filename:** `0976_Manual_HTTP_Automation_Webhook.json`  
**Description:** Scheduled automation that integrates with Httprequest for data processing. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (10 nodes)  
**Integrations:** Httprequest,  

---

### Manual Hackernews Create Triggered
**Filename:** `0996_Manual_Hackernews_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Hackernews to create new records. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Hackernews,  

---

### Expense Tracker App
**Filename:** `1030_HTTP_Typeform_Monitor_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Typeform, and Form Trigger for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,Typeform,Form Trigger,  

---

### Send a cocktail recipe every day via a Telegram
**Filename:** `1043_HTTP_Telegram_Send_Webhook.json`  
**Description:** Scheduled automation that connects Httprequest and Telegram for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (3 nodes)  
**Integrations:** Httprequest,Telegram,  

---

### Receive updates from Telegram and send an image of a cocktail
**Filename:** `1052_HTTP_Telegram_Update_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and Telegram to update existing data. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Httprequest,Telegram,  

---

### Syncro Status Update Clockify
**Filename:** `1064_HTTP_Clockify_Update_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and Clockify to update existing data. Uses 6 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,Webhook,Clockify,  

---

### Perplexity Researcher
**Filename:** `1072_HTTP_Stickynote_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that connects Executeworkflow and Httprequest for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Executeworkflow,Httprequest,  

---

### Manual HTTP Automation Webhook
**Filename:** `1074_Manual_HTTP_Automation_Webhook.json`  
**Description:** Manual workflow that integrates with Httprequest for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,  

---

### Manual HTTP Automation Webhook
**Filename:** `1080_Manual_HTTP_Automation_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Nextcloud for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,Nextcloud,  

---

### Creating your first workflow
**Filename:** `1101_Openweathermap_Twilio_Automate_Scheduled.json`  
**Description:** Scheduled automation that connects Twilio and Openweathermap for data processing. Uses 5 nodes.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** low (5 nodes)  
**Integrations:** Twilio,Openweathermap,  

---

### Dashboard
**Filename:** `1107_HTTP_GitHub_Automation_Webhook.json`  
**Description:** Scheduled automation that connects Httprequest and GitHub for data processing. Uses 24 nodes.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** high (24 nodes)  
**Integrations:** Httprequest,GitHub,  

---

### Remote IOT Sensor monitoring via MQTT and InfluxDB
**Filename:** `1110_HTTP_Mqtt_Monitor_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and Mqtt for monitoring and reporting. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,Mqtt,  

---

### Automating Betting Data Retrieval with TheOddsAPI and Airtable
**Filename:** `1111_HTTP_Schedule_Automation_Webhook.json`  
**Description:** Scheduled automation that connects Httprequest and Airtable for data processing. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (10 nodes)  
**Integrations:** Httprequest,Airtable,  

---

### AI Agent with charts capabilities using OpenAI Structured Output
**Filename:** `1112_HTTP_Stickynote_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Agent, and Toolworkflow for data processing. Uses 11 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** OpenAI,Agent,Toolworkflow,Chat,Executeworkflow,Memorybufferwindow,  

---

### Openweathermap Webhook Automate Webhook
**Filename:** `1118_Openweathermap_Webhook_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that connects Webhook and Openweathermap for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Webhook,Openweathermap,  

---

### Openweathermap Twilio Automate Webhook
**Filename:** `1119_Openweathermap_Twilio_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Airtable, Webhook, and Openweathermap for data processing. Uses 5 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Airtable,Webhook,Openweathermap,Twilio,  

---

### Extract information from an image of a receipt
**Filename:** `1128_Manual_HTTP_Automation_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Mindee for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Httprequest,Mindee,  

---

### get_a_web_page
**Filename:** `1131_HTTP_Stickynote_Import_Webhook.json`  
**Description:** Webhook-triggered automation that connects Executeworkflow and Httprequest for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Executeworkflow,Httprequest,  

---

### OpenAI ImageGen1 Template
**Filename:** `1152_HTTP_Stickynote_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Converttofile, and Chat for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,Converttofile,Chat,  

---

### Send daily weather updates via a push notification using the Pushcut node
**Filename:** `1156_Openweathermap_Cron_Update_Scheduled.json`  
**Description:** Scheduled automation that connects Pushcut and Openweathermap to update existing data. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (3 nodes)  
**Integrations:** Pushcut,Openweathermap,  

---

### Send daily weather updates to a phone number using the Vonage node
**Filename:** `1163_Openweathermap_Cron_Update_Scheduled.json`  
**Description:** Scheduled automation that connects Openweathermap and Vonage to update existing data. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (3 nodes)  
**Integrations:** Openweathermap,Vonage,  

---

### HTTP Cron Automation Scheduled
**Filename:** `1171_HTTP_Cron_Automation_Scheduled.json`  
**Description:** Scheduled automation that integrates with Httprequest for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,  

---

### Get the current weather data for a city
**Filename:** `1173_Manual_Openweathermap_Import_Triggered.json`  
**Description:** Manual workflow that integrates with Openweathermap for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Openweathermap,  

---

### Publish Videos & Images - Blotato
**Filename:** `1178_Code_HTTP_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Instagram, Twitter/X, and OpenAI for data processing. Uses 30 nodes and integrates with 8 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (30 nodes)  
**Integrations:** Instagram,Twitter/X,OpenAI,Airtable,LinkedIn,Httprequest,Form Trigger,Facebook,  

---

### Build your first AI MCP Server
**Filename:** `1184_Debughelper_HTTP_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Debughelper, and Agent for data processing. Uses 32 nodes and integrates with 12 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (32 nodes)  
**Integrations:** OpenAI,Debughelper,Agent,Toolworkflow,Mcpclienttool,Httprequest,Mcp,Chat,Googlecalendartool,Executeworkflow,Cal.com,Memorybufferwindow,  

---

### HTTP Dropbox Automation Webhook
**Filename:** `1187_HTTP_Dropbox_Automation_Webhook.json`  
**Description:** Manual workflow that orchestrates Compression, Dropbox, and Httprequest for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Compression,Dropbox,Httprequest,  

---

### HTTP Timescaledb Automation Scheduled
**Filename:** `1192_HTTP_Timescaledb_Automation_Scheduled.json`  
**Description:** Scheduled automation that connects Httprequest and Cal.com for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,Cal.com,  

---

### Send daily weather updates via a push notification
**Filename:** `1195_Openweathermap_Pushover_Update_Scheduled.json`  
**Description:** Scheduled automation that connects Openweathermap and Pushover to update existing data. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (3 nodes)  
**Integrations:** Openweathermap,Pushover,  

---

### Manual Peekalink Automate Triggered
**Filename:** `1204_Manual_Peekalink_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Peekalink for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Peekalink,  

---

### Openweathermap Webhook Create Webhook
**Filename:** `1222_Openweathermap_Webhook_Create_Webhook.json`  
**Description:** Webhook-triggered automation that connects Webhook and Openweathermap to create new records. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Webhook,Openweathermap,  

---

### Send updates about the position of the ISS every minute to a topic in Kafka
**Filename:** `1226_HTTP_Kafka_Update_Webhook.json`  
**Description:** Scheduled automation that connects Httprequest and Kafka to update existing data. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,Kafka,  

---

### HTTP Deepl Automation Webhook
**Filename:** `1233_HTTP_Deepl_Automation_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Deepl for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Httprequest,Deepl,  

---

### Google Trend Data Extract, Summarization with Bright Data & Google Gemini
**Filename:** `1235_Manual_HTTP_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Readwritefile, Lmchatgooglegemini, and Webhook for data processing. Uses 16 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** Readwritefile,Lmchatgooglegemini,Webhook,Gmail,Informationextractor,Chainllm,Form Trigger,Chainsummarization,  

---

### Convert YouTube Videos into SEO Blog Posts
**Filename:** `1241_Manual_HTTP_Process_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Markdown, and Gmail for data processing. Uses 15 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Httprequest,Markdown,Gmail,OpenAI,  

---

### Extractfromfile HTTP Automation Webhook
**Filename:** `1246_Extractfromfile_HTTP_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, OpenAI, and Vectorstoresupabase for data processing. Uses 33 nodes and integrates with 11 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (33 nodes)  
**Integrations:** Splitinbatches,OpenAI,Vectorstoresupabase,Extractfromfile,Agent,Httprequest,Documentdefaultdataloader,Textsplitterrecursivecharactertextsplitter,Chat,Toolvectorstore,Supabase,  

---

### Complete Youtube
**Filename:** `1264_Code_HTTP_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, OpenAI, and Agent for data processing. Uses 15 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Splitinbatches,OpenAI,Agent,Toolworkflow,Httprequest,Chat,Youtube,Memorybufferwindow,  

---

### Agent with custom HTTP Request
**Filename:** `1267_HTTP_Markdown_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, OpenAI, and Manualchat for data processing. Uses 20 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (20 nodes)  
**Integrations:** Markdown,OpenAI,Manualchat,Agent,Toolworkflow,Httprequest,Executeworkflow,  

---

### Stickynote Hackernews Automate Triggered
**Filename:** `1268_Stickynote_Hackernews_Automate_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Hackernews, OpenAI, and Manualchat for data processing. Uses 12 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Hackernews,OpenAI,Manualchat,Agent,Executeworkflow,Cal.com,  

---

### 🎥 Analyze YouTube Video for Summaries, Transcripts & Content + Google Gemini AI
**Filename:** `1313_Code_HTTP_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Google Drive, and Gmail for data processing. Uses 33 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (33 nodes)  
**Integrations:** Markdown,Google Drive,Gmail,Httprequest,Form Trigger,  

---

### Analyze Screenshots with AI
**Filename:** `1334_HTTP_Manual_Automation_Webhook.json`  
**Description:** Manual workflow that connects Box and OpenAI for data processing. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (9 nodes)  
**Integrations:** Box,OpenAI,  

---

### Fine-tuning with OpenAI models
**Filename:** `1339_Manual_HTTP_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates OpenAI, Google Drive, and Agent for data processing. Uses 9 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (9 nodes)  
**Integrations:** OpenAI,Google Drive,Agent,Httprequest,Chat,  

---

### [3/3] Anomaly detection tool (crops dataset)
**Filename:** `1340_HTTP_Executeworkflow_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and Executeworkflow for data processing. Uses 17 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (17 nodes)  
**Integrations:** Httprequest,Executeworkflow,  

---

### Weather via Slack
**Filename:** `1349_HTTP_Slack_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and Slack for data processing. Uses 5 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,Webhook,Slack,  

---

### Bitrix24 Chatbot Application Workflow example with Webhook Integration
**Filename:** `1354_HTTP_Respondtowebhook_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and Respondtowebhook for data processing. Uses 13 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (13 nodes)  
**Integrations:** Httprequest,Webhook,Respondtowebhook,  

---

### 💥workflow n8n d'Auto-Post sur les réseaux sociaux - vide
**Filename:** `1367_HTTP_Schedule_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Instagram, Google Sheets, and LinkedIn for data processing. Uses 15 nodes and integrates with 5 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Instagram,Google Sheets,LinkedIn,Httprequest,Facebook,  

---

### HTTP Extractfromfile Automation Webhook
**Filename:** `1370_HTTP_Extractfromfile_Automation_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Extractfromfile, and OpenAI for data processing. Uses 11 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (11 nodes)  
**Integrations:** Httprequest,Extractfromfile,OpenAI,  

---

### Scans von PDF zu Nextcloud
**Filename:** `1419_HTTP_Schedule_Automation_Scheduled.json`  
**Description:** Scheduled automation that connects Httprequest and Nextcloud for data processing. Uses 5 nodes.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,Nextcloud,  

---

### Manual HTTP Automation Webhook
**Filename:** `1436_Manual_HTTP_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Airtable, and Agent for data processing. Uses 29 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (29 nodes)  
**Integrations:** OpenAI,Airtable,Agent,Toolworkflow,Outputparserstructured,Httprequest,Executeworkflow,  

---

### HTTP Executeworkflow Automation Webhook
**Filename:** `1440_HTTP_Executeworkflow_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Agent, and Toolworkflow for data processing. Uses 29 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (29 nodes)  
**Integrations:** OpenAI,Agent,Toolworkflow,PostgreSQL,Httprequest,Chat,Executeworkflow,  

---

### URL Pinger
**Filename:** `1447_HTTP_Schedule_Automation_Webhook.json`  
**Description:** Scheduled automation that connects Httprequest and Splitout for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,Splitout,  

---

### Flux Dev Image Generation Fal.ai
**Filename:** `1456_Wait_HTTP_Automation_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Google Drive for data processing. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (12 nodes)  
**Integrations:** Httprequest,Google Drive,  

---

### Optimise images uploaded to GDrive
**Filename:** `1458_HTTP_Stickynote_Import_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and Google Drive for data processing. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Httprequest,Google Drive,  

---

### [3/3] Anomaly detection tool (crops dataset)
**Filename:** `1462_HTTP_Executeworkflow_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and Executeworkflow for data processing. Uses 17 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (17 nodes)  
**Integrations:** Httprequest,Executeworkflow,  

---

### AI-Powered Research with Jina AI Deep Search
**Filename:** `1464_HTTP_Stickynote_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Chat, and Form Trigger for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,Chat,Form Trigger,  

---

### HTTP Respondtowebhook Create Webhook
**Filename:** `1473_HTTP_Respondtowebhook_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and Respondtowebhook to create new records. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,Webhook,Respondtowebhook,  

---

### Send TTS (Text-to-speech) voice calls
**Filename:** `1481_HTTP_Form_Send_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and Form Trigger for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,Form Trigger,  

---

### Export Zammad Objects Users, Roles, Groups and Organizations to Excel
**Filename:** `1503_Manual_HTTP_Export_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Converttofile, and Zammad for data processing. Uses 18 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** high (18 nodes)  
**Integrations:** Httprequest,Converttofile,Zammad,  

---

### Dynamically create tables in Airtable for your Webflow form submissions
**Filename:** `1514_Code_HTTP_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Airtable, Webflow, and Form Trigger to create new records. Uses 17 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (17 nodes)  
**Integrations:** Airtable,Webflow,Form Trigger,  

---

### 🐋DeepSeek V3 Chat & R1 Reasoning Quick Start
**Filename:** `1519_HTTP_Stickynote_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatopenai, Agent, and Httprequest for data processing. Uses 15 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Lmchatopenai,Agent,Httprequest,Chainllm,Chat,Memorybufferwindow,Lmchatollama,  

---

### YouTube Video Transcriber
**Filename:** `1520_HTTP_Respondtowebhook_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Webhook, and OpenAI for data processing. Uses 14 nodes and integrates with 4 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Httprequest,Webhook,OpenAI,Chat,  

---

### HTTP Stickynote Automation Webhook
**Filename:** `1530_HTTP_Stickynote_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that connects Executeworkflow and Httprequest for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Executeworkflow,Httprequest,  

---

### 💥🛠️Automate Blog Content Creation with GPT-4, Perplexity & WordPress
**Filename:** `1535_HTTP_Form_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatopenai, Agent, and Httprequest for data processing. Uses 17 nodes and integrates with 9 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Lmchatopenai,Agent,Httprequest,Gmailtool,Wordpresstool,Chat,Form Trigger,Notion,Slack,  

---

### LINE Assistant with Google Calendar and Gmail Integration
**Filename:** `1538_HTTP_Googlecalendartool_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Webhook, and Agent for data processing. Uses 14 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** OpenAI,Webhook,Agent,Gmail,Httprequest,Toolwikipedia,Cal.com,Memorybufferwindow,  

---

### Splitout HTTP Create Webhook
**Filename:** `1542_Splitout_HTTP_Create_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Markdown, Lmchatgooglegemini, and Hackernews to create new records. Uses 10 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Markdown,Lmchatgooglegemini,Hackernews,Splitout,Emailsend,Httprequest,Chainllm,Form Trigger,  

---

### Merge PDFs
**Filename:** `1547_Manual_HTTP_Automation_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Readwritefile for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (7 nodes)  
**Integrations:** Httprequest,Readwritefile,  

---

### ✨📊Multi-AI Agent Chatbot for Postgres/Supabase DB and QuickCharts + Tool Router
**Filename:** `1558_HTTP_Stickynote_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Executeworkflow, Lmchatopenai, and Agent for data processing. Uses 40 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (40 nodes)  
**Integrations:** Executeworkflow,Lmchatopenai,Agent,Toolworkflow,Outputparserstructured,Httprequest,PostgreSQL,Chat,Postgrestool,  

---

### Open Deep Research - AI-Powered Autonomous Research Workflow
**Filename:** `1580_HTTP_Stickynote_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Lmchatopenrouter, and Agent for data processing. Uses 17 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Splitinbatches,Lmchatopenrouter,Agent,Chainllm,Chat,Form Trigger,Memorybufferwindow,  

---

### Manual HTTP Automation Webhook
**Filename:** `1584_Manual_HTTP_Automation_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Airtable, and Functionitem for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,Airtable,Functionitem,  

---

### Automated Daily Weather Data Fetcher and Storage
**Filename:** `1593_HTTP_Schedule_Import_Webhook.json`  
**Description:** Scheduled automation that connects Httprequest and Airtable for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,Airtable,  

---

### AirQuality Scheduler
**Filename:** `1598_HTTP_Schedule_Automation_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Toolthink, OpenAI, and Agent for data processing. Uses 12 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Toolthink,OpenAI,Agent,Gmail,Httprequest,  

---

### SSL Expiry Alert
**Filename:** `1614_Schedule_HTTP_Send_Webhook.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Google Sheets, and Gmail for notifications and alerts. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (12 nodes)  
**Integrations:** Httprequest,Google Sheets,Gmail,  

---

### Summarize emails with A.I. then send to messenger
**Filename:** `1615_HTTP_Emailreadimap_Send_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Email (IMAP), and Server-Sent Events for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (7 nodes)  
**Integrations:** Httprequest,Email (IMAP),Server-Sent Events,  

---

### 🔍🛠️ Tavily Search & Extract - Template
**Filename:** `1617_HTTP_Stickynote_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Chainllm, and OpenAI for data processing. Uses 17 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Httprequest,Chainllm,OpenAI,Chat,  

---

### NeurochainAI Basic API Integration
**Filename:** `1631_HTTP_Telegram_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and Telegram for data processing. Uses 29 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (29 nodes)  
**Integrations:** Httprequest,Telegram,  

---

### Telegram Tron Wallet Blacklist Checker
**Filename:** `1632_HTTP_Telegram_Monitor_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Telegram, and Form Trigger for data processing. Uses 7 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Httprequest,Telegram,Form Trigger,  

---

### Push Multiple Files to Github Repo via Github REST API
**Filename:** `1640_HTTP_Stickynote_Automation_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and GitHub for data processing. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (10 nodes)  
**Integrations:** Httprequest,GitHub,  

---

### Gratitude Jar Reminder
**Filename:** `1651_HTTP_Schedule_Automation_Webhook.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Chainllm, and OpenAI for data processing. Uses 9 nodes and integrates with 4 services.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** medium (9 nodes)  
**Integrations:** Httprequest,Chainllm,OpenAI,Form Trigger,  

---

### HTTP Telegram Send Webhook
**Filename:** `1654_HTTP_Telegram_Send_Webhook.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Airtable, and Telegram for data processing. Uses 15 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (15 nodes)  
**Integrations:** Httprequest,Airtable,Telegram,  

---

### HTTP Schedule Send Webhook
**Filename:** `1655_HTTP_Schedule_Send_Webhook.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Discord, and OpenAI for data processing. Uses 8 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (8 nodes)  
**Integrations:** Httprequest,Discord,OpenAI,Form Trigger,  

---

### Generate New Keywords with Search Volumes⚒️⚒️🟢🟢
**Filename:** `1660_Splitout_HTTP_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Splitout, and Google Sheets for data processing. Uses 11 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** Httprequest,Splitout,Google Sheets,Executeworkflow,  

---

### Code HTTP Send Webhook
**Filename:** `1664_Code_HTTP_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Twitter/X, OpenAI, and Google Sheets for data processing. Uses 19 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** Twitter/X,OpenAI,Google Sheets,LinkedIn,Emailsend,Outputparserstructured,Httprequest,Chainllm,  

---

### HTTP Form Automation Webhook
**Filename:** `1672_HTTP_Form_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Lmchatopenai, and Chainsummarization for data processing. Uses 10 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Httprequest,Lmchatopenai,Chainsummarization,Form Trigger,  

---

### Summarize emails with A.I. then send to messenger
**Filename:** `1674_HTTP_Emailreadimap_Send_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Email (IMAP), and Server-Sent Events for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (7 nodes)  
**Integrations:** Httprequest,Email (IMAP),Server-Sent Events,  

---

### Summarize emails with A.I. then send to messenger
**Filename:** `1675_HTTP_Emailreadimap_Send_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Email (IMAP), and Server-Sent Events for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (7 nodes)  
**Integrations:** Httprequest,Email (IMAP),Server-Sent Events,  

---

### NeurochainAI Basic API Integration
**Filename:** `1684_HTTP_Telegram_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and Telegram for data processing. Uses 29 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (29 nodes)  
**Integrations:** Httprequest,Telegram,  

---

### Telegram AI Langchain bot
**Filename:** `1687_HTTP_Telegram_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Telegram, and Agent for data processing. Uses 12 nodes and integrates with 7 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** OpenAI,Telegram,Agent,Toolworkflow,Httprequest,Executeworkflow,Memorybufferwindow,  

---

### HTTP Telegram Automate Webhook
**Filename:** `1688_HTTP_Telegram_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Telegram, OpenAI, and Supabase for data processing. Uses 17 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (17 nodes)  
**Integrations:** Telegram,OpenAI,Supabase,  

---

### Enrich Company Data from Google Sheet with OpenAI Agent and Scraper Tool
**Filename:** `1694_Webhook_HTTP_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Splitinbatches, and OpenAI for data processing. Uses 13 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** Markdown,Splitinbatches,OpenAI,Google Sheets,Agent,Webhook,Outputparserstructured,Httprequest,Cal.com,  

---

### Amazon Product Price Tracker
**Filename:** `1697_Schedule_HTTP_Monitor_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Splitinbatches, and Google Sheets for data processing. Uses 16 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** Httprequest,Splitinbatches,Google Sheets,Emailsend,  

---

### Transform Image to Lego Style Using Line and Dall-E
**Filename:** `1700_HTTP_Webhook_Process_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and OpenAI for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,Webhook,OpenAI,  

---

### get_a_web_page
**Filename:** `1720_HTTP_Stickynote_Import_Webhook.json`  
**Description:** Webhook-triggered automation that connects Executeworkflow and Httprequest for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Executeworkflow,Httprequest,  

---

### Convert Parquet, Avro, ORC & Feather via ParquetReader to JSON
**Filename:** `1725_HTTP_Code_Process_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and Webhook for data processing. Uses 4 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,Webhook,  

---

### [2/2] KNN classifier (lands dataset)
**Filename:** `1729_HTTP_Executeworkflow_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and Executeworkflow for data processing. Uses 18 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (18 nodes)  
**Integrations:** Httprequest,Executeworkflow,  

---

### [2/2] KNN classifier (lands dataset)
**Filename:** `1730_HTTP_Executeworkflow_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and Executeworkflow for data processing. Uses 18 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (18 nodes)  
**Integrations:** Httprequest,Executeworkflow,  

---

### [3/3] Anomaly detection tool (crops dataset)
**Filename:** `1732_HTTP_Executeworkflow_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and Executeworkflow for data processing. Uses 17 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (17 nodes)  
**Integrations:** Httprequest,Executeworkflow,  

---

### HTTP Stickynote Automation Webhook
**Filename:** `1737_HTTP_Stickynote_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Agent, and Chat for data processing. Uses 19 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** OpenAI,Agent,Chat,Form Trigger,Executeworkflow,Memorybufferwindow,Textclassifier,  

---

### Open Deep Research - AI-Powered Autonomous Research Workflow
**Filename:** `1747_HTTP_Stickynote_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Lmchatopenrouter, and Agent for data processing. Uses 17 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Splitinbatches,Lmchatopenrouter,Agent,Chainllm,Chat,Form Trigger,Memorybufferwindow,  

---

### Auto Knowledge Base Article Generator
**Filename:** `1756_Code_HTTP_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Agent, and Toolworkflow for data processing. Uses 23 nodes and integrates with 7 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (23 nodes)  
**Integrations:** OpenAI,Agent,Toolworkflow,Httprequest,Chat,Form Trigger,Executeworkflow,  

---

### Complete Youtube
**Filename:** `1758_Code_HTTP_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, OpenAI, and Agent for data processing. Uses 15 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Splitinbatches,OpenAI,Agent,Toolworkflow,Httprequest,Chat,Youtube,Memorybufferwindow,  

---

### Attachments Gmail to drive and google sheets
**Filename:** `1764_Extractfromfile_HTTP_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Drive, and Google Sheets for data processing. Uses 17 nodes and integrates with 8 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** OpenAI,Google Drive,Google Sheets,Gmail,Extractfromfile,Outputparserstructured,Httprequest,Chainllm,  

---

### Youtube Video Transcript Extraction
**Filename:** `1767_Form_HTTP_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and Form Trigger for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,Form Trigger,  

---

### Stripe Payment Order Sync – Auto Retrieve Customer & Product Purchased
**Filename:** `1773_HTTP_Stripe_Sync_Webhook.json`  
**Description:** Webhook-triggered automation that connects Stripe and Form Trigger to synchronize data. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Stripe,Form Trigger,  

---

### LINE Assistant with Google Calendar and Gmail Integration
**Filename:** `1778_HTTP_Googlecalendartool_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Webhook, and Agent for data processing. Uses 14 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** OpenAI,Webhook,Agent,Gmail,Httprequest,Toolwikipedia,Cal.com,Memorybufferwindow,  

---

### line message api demo
**Filename:** `1799_HTTP_Manual_Send_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and Webhook for data processing. Uses 8 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Httprequest,Webhook,  

---

### GROQ LLAVA V1.5 7B
**Filename:** `1800_HTTP_Telegram_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Telegram, and Extractfromfile for data processing. Uses 8 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Httprequest,Telegram,Extractfromfile,  

---

### Telegram AI Langchain bot
**Filename:** `1808_HTTP_Telegram_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Telegram, and Agent for data processing. Uses 12 nodes and integrates with 7 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** OpenAI,Telegram,Agent,Toolworkflow,Httprequest,Executeworkflow,Memorybufferwindow,  

---

### Line_Chatbot_Extract_Text_from_Pay_Slip_with_Gemini
**Filename:** `1811_HTTP_GoogleSheets_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Google Sheets, and Agent for data processing. Uses 17 nodes and integrates with 7 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Lmchatgooglegemini,Google Sheets,Agent,Webhook,Httprequest,Chainllm,Memorybufferwindow,  

---

### Update all Zammad Roles to default values
**Filename:** `1817_Manual_HTTP_Update_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Converttofile, and Zammad to update existing data. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (10 nodes)  
**Integrations:** Httprequest,Converttofile,Zammad,  

---

### Bitrix24 Chatbot Application Workflow example with Webhook Integration
**Filename:** `1827_HTTP_Respondtowebhook_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and Respondtowebhook for data processing. Uses 13 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (13 nodes)  
**Integrations:** Httprequest,Webhook,Respondtowebhook,  

---

### Connect Airtable Contacts to telli for Automated AI Voice Call Scheduling
**Filename:** `1852_HTTP_Stickynote_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Airtable, and Cal.com for data processing. Uses 7 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Httprequest,Airtable,Cal.com,  

---

### Fine-tuning with OpenAI models
**Filename:** `1863_Manual_HTTP_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates OpenAI, Google Drive, and Agent for data processing. Uses 9 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (9 nodes)  
**Integrations:** OpenAI,Google Drive,Agent,Httprequest,Chat,  

---

### YT New Video Upload
**Filename:** `1865_Code_HTTP_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, OpenAI, and Google Drive for data processing. Uses 14 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Lmchatgooglegemini,OpenAI,Google Drive,Agent,Httprequest,Form Trigger,Youtube,  

---

### Chatbot AI
**Filename:** `1868_HTTP_Stickynote_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Webhook, and Agent for data processing. Uses 14 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** OpenAI,Webhook,Agent,Httprequest,Form Trigger,  

---

### getBible Query v1.0
**Filename:** `1871_HTTP_Executeworkflow_Import_Webhook.json`  
**Description:** Webhook-triggered automation that connects Executeworkflow and Httprequest for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Executeworkflow,Httprequest,  

---

### 🤓 Conversion Rate Optimizer
**Filename:** `1873_Form_HTTP_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Agent, and OpenAI for data processing. Uses 4 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,Agent,OpenAI,Form Trigger,  

---

### 🎥 Gemini AI Video Analysis
**Filename:** `1876_HTTP_Manual_Automation_Webhook.json`  
**Description:** Manual workflow that integrates with Httprequest for data processing. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (10 nodes)  
**Integrations:** Httprequest,  

---

### Get PDF with JSReport
**Filename:** `1883_HTTP_Form_Import_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Gmail, and Form Trigger for data processing. Uses 5 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,Gmail,Form Trigger,  

---

### Chinese Translator
**Filename:** `1885_HTTP_Extractfromfile_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and Extractfromfile for data processing. Uses 21 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** high (21 nodes)  
**Integrations:** Httprequest,Webhook,Extractfromfile,  

---

### [2/2] KNN classifier (lands dataset)
**Filename:** `1890_HTTP_Executeworkflow_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and Executeworkflow for data processing. Uses 18 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (18 nodes)  
**Integrations:** Httprequest,Executeworkflow,  

---

### HTTP Gmail Automate Webhook
**Filename:** `1893_HTTP_Gmail_Automate_Webhook.json`  
**Description:** Scheduled automation that connects Httprequest and Gmail for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (7 nodes)  
**Integrations:** Httprequest,Gmail,  

---

### HTTP Telegram Automate Webhook
**Filename:** `1920_HTTP_Telegram_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Telegram, Webhook, and Emailsend for data processing. Uses 6 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Telegram,Webhook,Emailsend,Httprequest,Form Trigger,  

---

### Flux Dev Image Generation Fal.ai
**Filename:** `1931_Wait_HTTP_Automation_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Google Drive for data processing. Uses 12 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (12 nodes)  
**Integrations:** Httprequest,Google Drive,  

---

### Web Server Monitor.
**Filename:** `1952_Schedule_HTTP_Monitor_Webhook.json`  
**Description:** Scheduled automation that orchestrates Httprequest, Google Sheets, and Gmail for monitoring and reporting. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (7 nodes)  
**Integrations:** Httprequest,Google Sheets,Gmail,  

---

### HTTP Schedule Automate Webhook
**Filename:** `1959_HTTP_Schedule_Automate_Webhook.json`  
**Description:** Scheduled automation that connects Httprequest and Twilio for data processing. Uses 4 nodes.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,Twilio,  

---

### Google Maps FULL
**Filename:** `1964_HTTP_Aggregate_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatopenai, Google Sheets, and Agent for data processing. Uses 17 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Lmchatopenai,Google Sheets,Agent,Toolworkflow,Httprequest,Chat,Executeworkflow,Memorybufferwindow,Toolserpapi,  

---

### Manual HTTP Automate Webhook
**Filename:** `1971_Manual_HTTP_Automate_Webhook.json`  
**Description:** Manual workflow that integrates with Httprequest for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Httprequest,  

---

### upload-post images
**Filename:** `1973_HTTP_Manual_Import_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Instagram for data processing. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (10 nodes)  
**Integrations:** Httprequest,Instagram,  

---

### React to PDFMonkey Callback
**Filename:** `1976_HTTP_Stickynote_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and Webhook for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Httprequest,Webhook,  

---

### n8n Graphic Design Team
**Filename:** `1985_Converttofile_HTTP_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Converttofile, OpenAI, and Google Drive for data processing. Uses 37 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (37 nodes)  
**Integrations:** Converttofile,OpenAI,Google Drive,Google Sheets,Gmail,Outputparserstructured,Httprequest,Chainllm,Form Trigger,  

---

### Search news using Perplexity AI and post to X (Twitter)
**Filename:** `1989_HTTP_Schedule_Create_Webhook.json`  
**Description:** Scheduled automation that connects Httprequest and Twitter/X for data processing. Uses 5 nodes.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,Twitter/X,  

---

### Analyze Screenshots with AI
**Filename:** `1996_HTTP_Manual_Automation_Webhook.json`  
**Description:** Manual workflow that connects Box and OpenAI for data processing. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (9 nodes)  
**Integrations:** Box,OpenAI,  

---

### Upload video to drive via google script
**Filename:** `1999_Manual_HTTP_Import_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Google Drive for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Httprequest,Google Drive,  

---

### Line Chatbot Handling AI Responses with Groq and Llama3
**Filename:** `2019_HTTP_Stickynote_Automate_Webhook.json`  
**Description:** Webhook-triggered automation that connects Httprequest and Webhook for data processing. Uses 9 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (9 nodes)  
**Integrations:** Httprequest,Webhook,  

---

### Perform an email search with Icypeas (single)
**Filename:** `2032_Manual_HTTP_Send_Webhook.json`  
**Description:** Manual workflow that integrates with Httprequest for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,  

---

### 🐋DeepSeek V3 Chat & R1 Reasoning Quick Start
**Filename:** `2043_HTTP_Stickynote_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatopenai, Agent, and Httprequest for data processing. Uses 15 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Lmchatopenai,Agent,Httprequest,Chainllm,Chat,Memorybufferwindow,Lmchatollama,  

---

### Schedule HTTP Create Scheduled
**Filename:** `2045_Schedule_HTTP_Create_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Lmchatgooglegemini, Agent, and Html to create new records. Uses 8 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (8 nodes)  
**Integrations:** Lmchatgooglegemini,Agent,Html,Httprequest,Microsoftoutlook,  

---

### Deep Research Report Generation Using Open Router, Google Search, Webhook/Telegram and Notion
**Filename:** `2054_Deep_Research_Report_Generation_With_Open_Router_Google_Search_Webhook_Telegram_and_Notion.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Splitinbatches, and Lmchatgooglegemini for data processing. Uses 38 nodes and integrates with 13 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (38 nodes)  
**Integrations:** Markdown,Splitinbatches,Lmchatgooglegemini,OpenAI,Lmchatopenrouter,Telegram,Splitout,Agent,Webhook,Outputparserstructured,Httprequest,Notion,Memorybufferwindow,  

---


## Summary

**Total Web Scraping & Data Extraction workflows:** 264  
**Documentation generated:** 2025-07-27 14:45:10  
**API Source:** https://scan-might-updates-postage.trycloudflare.com/api  

This documentation was automatically generated using the n8n workflow API endpoints.
