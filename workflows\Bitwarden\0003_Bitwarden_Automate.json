{"nodes": [{"name": "Bitwarden", "type": "n8n-nodes-base.bitwarden", "position": [470, 320], "parameters": {"name": "documentation", "resource": "group", "operation": "create", "additionalFields": {}}, "credentials": {"bitwardenApi": "Bitwarden API Credentials"}, "typeVersion": 1}, {"name": "Bitwarden1", "type": "n8n-nodes-base.bitwarden", "position": [670, 320], "parameters": {"resource": "member", "operation": "getAll", "returnAll": true}, "credentials": {"bitwardenApi": "Bitwarden API Credentials"}, "typeVersion": 1}, {"name": "Bitwarden2", "type": "n8n-nodes-base.bitwarden", "position": [870, 320], "parameters": {"groupId": "={{$node[\"Bitwarden\"].json[\"id\"]}}", "resource": "group", "memberIds": "={{$json[\"id\"]}}", "operation": "updateMembers"}, "credentials": {"bitwardenApi": "Bitwarden API Credentials"}, "typeVersion": 1}, {"name": "Bitwarden3", "type": "n8n-nodes-base.bitwarden", "position": [1070, 320], "parameters": {"groupId": "={{$node[\"Bitwarden\"].json[\"id\"]}}", "resource": "group", "operation": "getMembers"}, "credentials": {"bitwardenApi": "Bitwarden API Credentials"}, "typeVersion": 1}], "connections": {"Bitwarden": {"main": [[{"node": "Bitwarden1", "type": "main", "index": 0}]]}, "Bitwarden1": {"main": [[{"node": "Bitwarden2", "type": "main", "index": 0}]]}, "Bitwarden2": {"main": [[{"node": "Bitwarden3", "type": "main", "index": 0}]]}}}