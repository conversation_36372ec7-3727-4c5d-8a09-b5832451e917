{"id": "5Y8QXJ3N67wnmR2R", "meta": {"instanceId": "433fa4b57c582f828a127c9c601af0fc38d9d6424efd30a3ca802a4cc3acd656", "templateCredsSetupCompleted": true}, "name": "POC - Chatbot Order by Sheet Data", "tags": [], "nodes": [{"id": "cc9ab139-303f-411a-a7c8-5985d92e3040", "name": "Calculator", "type": "@n8n/n8n-nodes-langchain.toolCalculator", "position": [1460, 480], "parameters": {}, "typeVersion": 1}, {"id": "97a6d3a8-001c-4c62-84c2-da5b46a286a9", "name": "Chat OpenAI", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [740, 480], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "XXXXXXXXXX", "name": "OpenAI Credentials"}}, "typeVersion": 1}, {"id": "1ad05eb6-0f6a-4da7-9d86-871dfa7cbce1", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [900, 480], "parameters": {}, "typeVersion": 1.2}, {"id": "f4883308-3e4a-49b1-82f5-c18dc2121c47", "name": "Get Products", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1060, 480], "parameters": {"url": "https://n8n.io/webhook/get-products", "toolDescription": "Retrieve detailed information about the product menu."}, "typeVersion": 1.1}, {"id": "058b1cf5-b8c0-414d-b4c6-e4c016e4d181", "name": "Order Product", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1200, 480], "parameters": {"url": "https://n8n.io/webhook/order-product", "method": "POST", "sendBody": true, "parametersBody": {"values": [{"name": "message", "value": "={{ $json.chatInput }}", "valueProvider": "fieldValue"}]}, "toolDescription": "Process product orders."}, "typeVersion": 1.1}, {"id": "6e0b433c-1d8f-4cf8-aa06-cc1b8d51e2d9", "name": "Get Order", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1320, 480], "parameters": {"url": "https://n8n.io/webhook/get-orders", "toolDescription": "Get the order status."}, "typeVersion": 1.1}, {"id": "a0ee2e49-52cf-40d8-b108-4357bf562505", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [540, 160], "webhookId": "d925cc6e-6dd7-4459-a917-e68d57ab0e2a", "parameters": {"public": true, "options": {}, "initialMessages": "Hellooo! 👋 My name is <PERSON><PERSON> 🍕. I'm here to help with your pizza order. How can I assist you?\n\n📣 INFO: If you’d like to order a pizza, please include your name + pizza type + quantity. Thank you!"}, "typeVersion": 1.1}, {"id": "81892405-e09c-4452-99b3-f5edbe49b830", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [780, 160], "parameters": {"text": "={{ $json.chatInput }}", "options": {"systemMessage": "=Your name is <PERSON><PERSON>, and you are an assistant for handling customer pizza orders.\n\n1. If a customer asks about the menu, provide information on the available products.\n2. If a customer is placing an order, confirm the order details, inform them that the order is being processed, and thank them.\n3. If a customer inquires about their order status, provide the order date, pizza type, and quantity."}, "promptType": "define"}, "executeOnce": false, "typeVersion": 1.6}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "6431e20b-e135-43b2-bbcb-ed9c705d1237", "connections": {"Get Order": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Calculator": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Chat OpenAI": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Get Products": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Order Product": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}}