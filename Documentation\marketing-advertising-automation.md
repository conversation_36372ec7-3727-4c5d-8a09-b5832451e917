# Marketing & Advertising Automation - N8N Workflows

## Overview
This document catalogs the **Marketing & Advertising Automation** workflows from the n8n Community Workflows repository.

**Category:** Marketing & Advertising Automation  
**Total Workflows:** 143  
**Generated:** 2025-07-27  
**Source:** https://scan-might-updates-postage.trycloudflare.com/api

---

## Workflows

### Look up a person using their email in Clearbit
**Filename:** `0024_Manual_Clearbit_Send_Triggered.json`  
**Description:** Manual workflow that integrates with Clearbit for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Clearbit,  

---

### Mailcheck Airtable Monitor
**Filename:** `0026_Mailcheck_Airtable_Monitor.json`  
**Description:** Manual workflow that connects Airtable and Mailcheck for monitoring and reporting. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Airtable,Mailcheck,  

---

### Manual Ical Send Triggered
**Filename:** `0038_Manual_Ical_Send_Triggered.json`  
**Description:** Manual workflow that connects Cal.com and Emailsend for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Cal.com,Emailsend,  

---

### Receive updates when a new account is added by an admin in ActiveCampaign
**Filename:** `0057_Activecampaign_Create_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Activecampaign to update existing data. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Activecampaign,  

---

### Create Email Campaign From LinkedIn Post Interactions
**Filename:** `0090_Wait_Lemlist_Create_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Hubspot, Airtable, and Lemlist to create new records. Uses 16 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** Hubspot,Airtable,Lemlist,LinkedIn,Dropcontact,Phantombuster,  

---

### Create a user profile in Vero
**Filename:** `0111_Manual_Vero_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Vero to create new records. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Vero,  

---

### Get information about a company with UpLead
**Filename:** `0117_Manual_Uplead_Import_Triggered.json`  
**Description:** Manual workflow that integrates with Uplead for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Uplead,  

---

### Receive messages for an ActiveMQ queue via AMQP Trigger
**Filename:** `0138_Amqp_Send_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Amqp for data processing. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Amqp,  

---

### Manual Send Triggered
**Filename:** `0145_Manual_Send_Triggered.json`  
**Description:** Manual workflow that for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** None  

---

### Receive updates when a form is submitted in Mautic, and send a confirmation SMS
**Filename:** `0155_Mautic_Twilio_Update_Triggered.json`  
**Description:** Webhook-triggered automation that connects Twilio and Mautic to update existing data. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (2 nodes)  
**Integrations:** Twilio,Mautic,  

---

### Create, update and get a user from Iterable
**Filename:** `0208_Manual_Iterable_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Iterable to create new records. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Iterable,  

---

### Receive messages from a topic and send an SMS
**Filename:** `0209_Noop_Kafka_Send_Triggered.json`  
**Description:** Webhook-triggered automation that connects Kafka and Vonage for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Kafka,Vonage,  

---

### Create a short URL and get the statistics of the URL
**Filename:** `0210_Manual_Yourls_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Yourls to create new records. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Yourls,  

---

### Mautic Mondaycom Create Triggered
**Filename:** `0275_Mautic_Mondaycom_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Monday.com and Mautic to create new records. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Monday.com,Mautic,  

---

### Calendly Mautic Create Triggered
**Filename:** `0277_Calendly_Mautic_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Calendly and Mautic to create new records. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Calendly,Mautic,  

---

### Lemlist Slack Create Webhook
**Filename:** `0283_Lemlist_Slack_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Hubspot, OpenAI, and Lemlist to create new records. Uses 12 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Hubspot,OpenAI,Lemlist,Httprequest,Slack,  

---

### Code Readpdf Send Triggered
**Filename:** `0298_Code_Readpdf_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Gmail, OpenAI, and Google Drive for data processing. Uses 18 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** Gmail,OpenAI,Google Drive,Readpdf,  

---

### Send Triggered
**Filename:** `0320_Send_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates OpenAI, Agent, and Chat for data processing. Uses 5 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** OpenAI,Agent,Chat,Memorybufferwindow,Toolserpapi,  

---

### Splitout Code Send Triggered
**Filename:** `0322_Splitout_Code_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Splitout, and Agent for data processing. Uses 18 nodes and integrates with 10 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** OpenAI,Splitout,Agent,Gmail,Informationextractor,Documentdefaultdataloader,Textsplitterrecursivecharactertextsplitter,Form Trigger,Toolwikipedia,Chainsummarization,  

---

### Stickynote Send Triggered
**Filename:** `0325_Stickynote_Send_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates OpenAI, Agent, and Chat for data processing. Uses 9 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (9 nodes)  
**Integrations:** OpenAI,Agent,Chat,Toolwikipedia,Memorybufferwindow,Toolserpapi,  

---

### Manual Stickynote Send Triggered
**Filename:** `0326_Manual_Stickynote_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Drive, and Agent for data processing. Uses 15 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** OpenAI,Google Drive,Agent,Vectorstorepinecone,Documentdefaultdataloader,Chat,Textsplitterrecursivecharactertextsplitter,  

---

### Manual Send Triggered
**Filename:** `0329_Manual_Send_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Toolcode, Agent, and OpenAI for data processing. Uses 6 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Toolcode,Agent,OpenAI,Chat,  

---

### Wait Webhook Send Webhook
**Filename:** `0330_Wait_Webhook_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Form Trigger, Webhook, and Gmail for data processing. Uses 29 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (29 nodes)  
**Integrations:** Form Trigger,Webhook,Gmail,Httprequest,Itemlists,Slack,  

---

### Stopanderror Extractfromfile Send Webhook
**Filename:** `0331_Stopanderror_Extractfromfile_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Extractfromfile, and Html for data processing. Uses 24 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (24 nodes)  
**Integrations:** OpenAI,Extractfromfile,Html,Emailsend,Httprequest,Form Trigger,  

---

### Stickynote Send Triggered
**Filename:** `0332_Stickynote_Send_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Chainllm, Lmopenhuggingfaceinference, and Chat for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Chainllm,Lmopenhuggingfaceinference,Chat,  

---

### Create entry in Mailchimp from Airtable
**Filename:** `0345_Mailchimp_Cron_Create_Scheduled.json`  
**Description:** Scheduled automation that connects Airtable and Mailchimp to create new records. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (3 nodes)  
**Integrations:** Airtable,Mailchimp,  

---

### Send a message on Twake
**Filename:** `0355_Manual_Twake_Send_Triggered.json`  
**Description:** Manual workflow that integrates with Twake for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Twake,  

---

### Email form
**Filename:** `0361_Hunter_Noop_Send_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Sendgrid, Hunter, and Form Trigger for data processing. Uses 7 nodes.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Sendgrid,Hunter,Form Trigger,  

---

### Code Manual Send Webhook
**Filename:** `0365_Code_Manual_Send_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Google Sheets for data processing. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (8 nodes)  
**Integrations:** Httprequest,Google Sheets,  

---

### Code Manual Send Webhook
**Filename:** `0367_Code_Manual_Send_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Google Sheets for data processing. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (8 nodes)  
**Integrations:** Httprequest,Google Sheets,  

---

### Manual Stickynote Send Webhook
**Filename:** `0374_Manual_Stickynote_Send_Webhook.json`  
**Description:** Manual workflow that orchestrates Httprequest, Box, and Form Trigger for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (7 nodes)  
**Integrations:** Httprequest,Box,Form Trigger,  

---

### Webhook Code Send Webhook
**Filename:** `0375_Webhook_Code_Send_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Respondtowebhook, and Form Trigger for data processing. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Httprequest,Respondtowebhook,Form Trigger,  

---

### Wait Code Send Scheduled
**Filename:** `0385_Wait_Code_Send_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Gmail, and Htmlextract for data processing. Uses 15 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Httprequest,Gmail,Htmlextract,Itemlists,  

---

### Code Filter Send Triggered
**Filename:** `0401_Code_Filter_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Sheets, and Agent for data processing. Uses 20 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (20 nodes)  
**Integrations:** OpenAI,Google Sheets,Agent,Toolworkflow,Chat,Executeworkflow,Memorybufferwindow,  

---

### Stickynote Send Triggered
**Filename:** `0407_Stickynote_Send_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Cal.com, Memorybufferwindow, and OpenAI for data processing. Uses 6 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Cal.com,Memorybufferwindow,OpenAI,Chat,  

---

### Create, update and get a contact using the SendGrid node
**Filename:** `0408_Manual_Sendgrid_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Sendgrid to create new records. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Sendgrid,  

---

### Filter Form Send Triggered
**Filename:** `0411_Filter_Form_Send_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Clearbit, Gmail, and Form Trigger for data processing. Uses 13 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (13 nodes)  
**Integrations:** Clearbit,Gmail,Form Trigger,  

---

### Hunter Form Create Triggered
**Filename:** `0420_Hunter_Form_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Hubspot, Clearbit, and Hunter to create new records. Uses 11 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** Hubspot,Clearbit,Hunter,Form Trigger,  

---

### Hunter Form Send Webhook
**Filename:** `0424_Hunter_Form_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Httprequest, Gmail, and Hunter for data processing. Uses 12 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Httprequest,Gmail,Hunter,Form Trigger,  

---

### Hunter Form Send Webhook
**Filename:** `0426_Hunter_Form_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Hubspot, Hunter, and Gmail for data processing. Uses 15 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Hubspot,Hunter,Gmail,Httprequest,Form Trigger,  

---

### Filter Convertkit Create Triggered
**Filename:** `0431_Filter_Convertkit_Create_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Hubspot, Clearbit, and Convertkit to create new records. Uses 16 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (16 nodes)  
**Integrations:** Hubspot,Clearbit,Convertkit,  

---

### Hunter Pipedrive Create Triggered
**Filename:** `0436_Hunter_Pipedrive_Create_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Clearbit, Hunter, and Pipedrive to create new records. Uses 15 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (15 nodes)  
**Integrations:** Clearbit,Hunter,Pipedrive,Form Trigger,  

---

### Splitout Schedule Send Scheduled
**Filename:** `0442_Splitout_Schedule_Send_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Splitinbatches, Splitout, and Gmail for data processing. Uses 10 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (10 nodes)  
**Integrations:** Splitinbatches,Splitout,Gmail,Rssfeedread,  

---

### Wait Filter Send Webhook
**Filename:** `0466_Wait_Filter_Send_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Google Drive for data processing. Uses 14 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (14 nodes)  
**Integrations:** Httprequest,Google Drive,  

---

### Webhook Respondtowebhook Send Webhook
**Filename:** `0467_Webhook_Respondtowebhook_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Google Drive, and Webhook for data processing. Uses 17 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** OpenAI,Google Drive,Webhook,Chainretrievalqa,Documentdefaultdataloader,Vectorstoreqdrant,Textsplitterrecursivecharactertextsplitter,Chat,Retrievervectorstore,  

---

### Unsubscribe Mautic contacts from automated unsubscribe emails
**Filename:** `0490_Mautic_Gmail_Send_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Server-Sent Events, Gmail, and Mautic for data processing. Uses 16 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (16 nodes)  
**Integrations:** Server-Sent Events,Gmail,Mautic,  

---

### Splitout Schedule Send Scheduled
**Filename:** `0500_Splitout_Schedule_Send_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Markdown, GitHub, and Splitout for data processing. Uses 7 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (7 nodes)  
**Integrations:** Markdown,GitHub,Splitout,Gmail,  

---

### Manual Extractfromfile Send Webhook
**Filename:** `0501_Manual_Extractfromfile_Send_Webhook.json`  
**Description:** Manual workflow that connects Httprequest and Extractfromfile for data processing. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (10 nodes)  
**Integrations:** Httprequest,Extractfromfile,  

---

### Lemlist Slack Create Webhook
**Filename:** `0504_Lemlist_Slack_Create_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Lemlist, and Outputparserstructured to create new records. Uses 18 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** OpenAI,Lemlist,Outputparserstructured,Httprequest,Chainllm,Form Trigger,Slack,  

---

### Localfile Splitout Send Triggered
**Filename:** `0536_Localfile_Splitout_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Readwritefile, OpenAI, and Splitout for data processing. Uses 17 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Readwritefile,OpenAI,Splitout,Agent,Extractfromfile,Outputparserstructured,Localfile,Toolcode,  

---

### Wait Splitout Send Webhook
**Filename:** `0538_Wait_Splitout_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, OpenAI, and Compression for data processing. Uses 38 nodes and integrates with 15 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (38 nodes)  
**Integrations:** Splitinbatches,OpenAI,Compression,Splitout,Embeddingsmistralcloud,Extractfromfile,Agent,Toolworkflow,Httprequest,Documentdefaultdataloader,Vectorstoreqdrant,Textsplitterrecursivecharactertextsplitter,Chat,Executeworkflow,Memorybufferwindow,  

---

### Code Schedule Send Scheduled
**Filename:** `0553_Code_Schedule_Send_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Ftp, Converttofile, and Mqtt for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (6 nodes)  
**Integrations:** Ftp,Converttofile,Mqtt,  

---

### Code Webhook Send Webhook
**Filename:** `0571_Code_Webhook_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Airtable, Webhook, and Gmail for data processing. Uses 13 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (13 nodes)  
**Integrations:** Airtable,Webhook,Gmail,Html,  

---

### Filter Schedule Send Scheduled
**Filename:** `0572_Filter_Schedule_Send_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Notion, Html, and Emailsend for data processing. Uses 27 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (27 nodes)  
**Integrations:** Notion,Html,Emailsend,Pushover,  

---

### Stickynote Notion Send Webhook
**Filename:** `0573_Stickynote_Notion_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Toolhttprequest, OpenAI, and Agent for data processing. Uses 12 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (12 nodes)  
**Integrations:** Toolhttprequest,OpenAI,Agent,Chat,Form Trigger,Notion,Memorybufferwindow,  

---

### Splitout Filter Send Webhook
**Filename:** `0587_Splitout_Filter_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Splitout, and Email (IMAP) for data processing. Uses 11 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** OpenAI,Splitout,Email (IMAP),Httprequest,Box,  

---

### Respondtowebhook Stickynote Send Webhook
**Filename:** `0590_Respondtowebhook_Stickynote_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Toolhttprequest, OpenAI, and Webhook for data processing. Uses 28 nodes and integrates with 10 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (28 nodes)  
**Integrations:** Toolhttprequest,OpenAI,Webhook,Agent,Toolworkflow,Chat,Form Trigger,Cal.com,Notion,Memorybufferwindow,  

---

### Filter Manual Send Triggered
**Filename:** `0595_Filter_Manual_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Splitinbatches, and Agent for data processing. Uses 36 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (36 nodes)  
**Integrations:** Markdown,Splitinbatches,Agent,Outlook,Lmchatollama,  

---

### Wait Splitout Send Webhook
**Filename:** `0602_Wait_Splitout_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Splitout, and Agent for data processing. Uses 35 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (35 nodes)  
**Integrations:** Lmchatgooglegemini,Splitout,Agent,Httprequest,Chainllm,WhatsApp,Form Trigger,Toolwikipedia,Memorybufferwindow,  

---

### Google Maps Email Scraper Template
**Filename:** `0639_Wait_Splitout_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Splitout, and Google Sheets for data processing. Uses 26 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (26 nodes)  
**Integrations:** Splitinbatches,Splitout,Google Sheets,Httprequest,Removeduplicates,Executeworkflow,  

---

### Add subscriber to form, create tag and subscriber to the tag
**Filename:** `0653_Manual_Convertkit_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Convertkit to create new records. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Convertkit,  

---

### Stickynote Respondtowebhook Send Webhook
**Filename:** `0684_Stickynote_Respondtowebhook_Send_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Webhook, Respondtowebhook, and Slack for data processing. Uses 10 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Webhook,Respondtowebhook,Slack,Servicenow,  

---

### Limit Webhook Send Webhook
**Filename:** `0685_Limit_Webhook_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Webhook, and Respondtowebhook for data processing. Uses 29 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (29 nodes)  
**Integrations:** Splitinbatches,Webhook,Respondtowebhook,Httprequest,Servicenow,Form Trigger,Slack,  

---

### Code Respondtowebhook Send Webhook
**Filename:** `0700_Code_Respondtowebhook_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Toolhttprequest, OpenAI, and Webhook for data processing. Uses 24 nodes and integrates with 11 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (24 nodes)  
**Integrations:** Toolhttprequest,OpenAI,Webhook,Agent,Toolworkflow,Respondtowebhook,Httprequest,Chat,Executeworkflow,Memorybufferwindow,Microsoftoutlook,  

---

### Receive updates when a subscriber is added through a form in ConvertKit
**Filename:** `0723_Convertkit_Create_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Convertkit to update existing data. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Convertkit,  

---

### Schedule Stickynote Send Scheduled
**Filename:** `0729_Schedule_Stickynote_Send_Scheduled.json`  
**Description:** Scheduled automation that connects Ssh and Emailsend for data processing. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (10 nodes)  
**Integrations:** Ssh,Emailsend,  

---

### Splitout Noop Send Triggered
**Filename:** `0730_Splitout_Noop_Send_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Splitout, Gmail, and Google Drive for data processing. Uses 8 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Splitout,Gmail,Google Drive,  

---

### Receive updates when a subscriber unsubscribes in Customer.io
**Filename:** `0738_Customerio_Update_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Customerio to update existing data. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Customerio,  

---

### SIGNL4 Alert
**Filename:** `0749_Readbinaryfile_Movebinarydata_Send_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Readbinaryfile, Signl4, and Writebinaryfile for notifications and alerts. Uses 9 nodes and integrates with 4 services.  
**Status:** Active  
**Trigger:** Scheduled  
**Complexity:** medium (9 nodes)  
**Integrations:** Readbinaryfile,Signl4,Writebinaryfile,Movebinarydata,  

---

### Stickynote Send Webhook
**Filename:** `0755_Stickynote_Send_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Toolhttprequest, OpenAI, and Agent for data processing. Uses 6 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Toolhttprequest,OpenAI,Agent,Toolworkflow,Chat,  

---

### Splitout Code Send Webhook
**Filename:** `0760_Splitout_Code_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Salesforce, Httprequest, and Executeworkflow for data processing. Uses 23 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (23 nodes)  
**Integrations:** Salesforce,Httprequest,Executeworkflow,Cal.com,Notion,  

---

### Code Filter Send Webhook
**Filename:** `0767_Code_Filter_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Googlesheetstool, OpenAI, and Agent for data processing. Uses 30 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (30 nodes)  
**Integrations:** Googlesheetstool,OpenAI,Agent,Toolworkflow,Httprequest,Chat,Executeworkflow,Cal.com,Memorybufferwindow,  

---

### Receive updates when a subscriber is added to a group and strore the information in Airtable
**Filename:** `0776_Manual_Mailerlite_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Airtable and Mailerlite to update existing data. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (4 nodes)  
**Integrations:** Airtable,Mailerlite,  

---

### Splitout Code Send Triggered
**Filename:** `0793_Splitout_Code_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Google Sheets, Gmail, and Splitout for data processing. Uses 18 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** Google Sheets,Gmail,Splitout,Form Trigger,  

---

### Schedule Mailchimp Create Scheduled
**Filename:** `0795_Schedule_Mailchimp_Create_Scheduled.json`  
**Description:** Scheduled automation that orchestrates Splitinbatches, Google Sheets, and Mailchimp to create new records. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (6 nodes)  
**Integrations:** Splitinbatches,Google Sheets,Mailchimp,  

---

### Create a customer and add them to a segment in Customer.io
**Filename:** `0803_Manual_Customerio_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Customerio to create new records. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Customerio,  

---

### Send Triggered
**Filename:** `0804_Send_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Agent, OpenAI, and Chat for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Agent,OpenAI,Chat,  

---

### Code Form Send Webhook
**Filename:** `0808_Code_Form_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Agent, and Gmail for data processing. Uses 19 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** OpenAI,Agent,Gmail,Httprequest,Form Trigger,  

---

### Wait Code Send Webhook
**Filename:** `0820_Wait_Code_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Splitinbatches, and Lmchatgooglegemini for data processing. Uses 24 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (24 nodes)  
**Integrations:** Markdown,Splitinbatches,Lmchatgooglegemini,Microsoftexcel,Extractfromfile,Httprequest,Textclassifier,Microsoftoutlook,  

---

### Filter Summarize Send Scheduled
**Filename:** `0830_Filter_Summarize_Send_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Googlesheetstool, Google Sheets, and Gmail for data processing. Uses 20 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (20 nodes)  
**Integrations:** Googlesheetstool,Google Sheets,Gmail,Extractfromfile,Informationextractor,Form Trigger,Cal.com,  

---

### Splitout Limit Send Webhook
**Filename:** `0860_Splitout_Limit_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Vectorstoremilvus, and Splitout for data processing. Uses 22 nodes and integrates with 10 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (22 nodes)  
**Integrations:** OpenAI,Vectorstoremilvus,Splitout,Chainretrievalqa,Html,Httprequest,Documentdefaultdataloader,Chat,Textsplitterrecursivecharactertextsplitter,Retrievervectorstore,  

---

### Wait Datetime Send Scheduled
**Filename:** `0869_Wait_Datetime_Send_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Datetime, and Toolthink for data processing. Uses 30 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (30 nodes)  
**Integrations:** Splitinbatches,Datetime,Toolthink,Lmchatgooglegemini,Html,Outlook,MySQL,Form Trigger,Cal.com,  

---

### Wait Code Send Webhook
**Filename:** `0888_Wait_Code_Send_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and Emailsend for data processing. Uses 15 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (15 nodes)  
**Integrations:** Httprequest,Webhook,Emailsend,  

---

### Form Stickynote Send Triggered
**Filename:** `0890_Form_Stickynote_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Hubspot, OpenAI, and Agent for data processing. Uses 14 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (14 nodes)  
**Integrations:** Hubspot,OpenAI,Agent,Gmail,Form Trigger,Chainsummarization,  

---

### Limit Code Send Scheduled
**Filename:** `0897_Limit_Code_Send_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Discord, and Google Drive for data processing. Uses 29 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (29 nodes)  
**Integrations:** Splitinbatches,Discord,Google Drive,Gmail,Executiondata,Server-Sent Events,N8N,Executeworkflow,  

---

### Bitly Datetime Update Webhook
**Filename:** `0910_Bitly_Datetime_Update_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Twitter/X, Dropbox, and Toolhttprequest to update existing data. Uses 113 nodes and integrates with 61 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (113 nodes)  
**Integrations:** Twitter/X,Dropbox,Toolhttprequest,Vectorstoreinmemory,Converttofile,Google Drive,Splitinbatches,Rssfeedread,Google Sheets,Webhook,Chainretrievalqa,Executiondata,Bitly,Emailsendtool,Removeduplicates,Pushbullet,Cal.com,Toolcode,Gumroad,Google Calendar,Markdown,Memorymanager,Datetime,Executecommand,OpenAI,Toolwolframalpha,Email (IMAP),Vectorstoresupabase,Anthropic,Httprequest,Renamekeys,Form Trigger,Mcpclienttool,Chainsummarization,Executeworkflow,Lmchatgooglegemini,Splitout,Agent,Gmail,Vectorstorepinecone,Outputparseritemlist,Outputparserstructured,PostgreSQL,Chainllm,Reddit,Documentdefaultdataloader,Chat,Toolwikipedia,Sentimentanalysis,Redis,Textclassifier,Toolserpapi,Ftp,Google Docs,Extractfromfile,Html,Outputparserautofixing,Youtube,Embeddingsgooglegemini,Memorybufferwindow,Toolvectorstore,  

---

### Code Noop Send Triggered
**Filename:** `0918_Code_Noop_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, OpenAI, and Google Sheets for data processing. Uses 19 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** Splitinbatches,OpenAI,Google Sheets,Agent,Chat,Form Trigger,Executeworkflow,Cal.com,Memorybufferwindow,  

---

### Splitout Code Send Scheduled
**Filename:** `0921_Splitout_Code_Send_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Lmchatgooglegemini, and Splitout for data processing. Uses 47 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (47 nodes)  
**Integrations:** Splitinbatches,Lmchatgooglegemini,Splitout,Chainllm,Executeworkflow,Slack,  

---

### Splitout Code Send Scheduled
**Filename:** `0923_Splitout_Code_Send_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, OpenAI, and Microsoftteams for data processing. Uses 17 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Markdown,OpenAI,Microsoftteams,Splitout,Chainllm,  

---

### Mailchimp
**Filename:** `0938_Manual_Mailchimp_Automation_Triggered.json`  
**Description:** Manual workflow that integrates with Mailchimp for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Mailchimp,  

---

### Analyze_email_headers_for_IPs_and_spoofing__3
**Filename:** `0946_Code_Webhook_Send_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Webhook, and Itemlists for data processing. Uses 35 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (35 nodes)  
**Integrations:** Httprequest,Webhook,Itemlists,  

---

### Manual Activecampaign Automation Triggered
**Filename:** `0951_Manual_Activecampaign_Automation_Triggered.json`  
**Description:** Manual workflow that integrates with Activecampaign for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Activecampaign,  

---

### Mautic Webhook Update Webhook
**Filename:** `0963_Mautic_Webhook_Update_Webhook.json`  
**Description:** Webhook-triggered automation that connects Webhook and Mautic to update existing data. Uses 17 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** high (17 nodes)  
**Integrations:** Webhook,Mautic,  

---

### Manual Awsses Automate Triggered
**Filename:** `0983_Manual_Awsses_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Awsses for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Awsses,  

---

### Send an SMS using MSG91
**Filename:** `0986_Manual_Msg91_Send_Triggered.json`  
**Description:** Manual workflow that integrates with Msg91 for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Msg91,  

---

### Mailchimp Automate Triggered
**Filename:** `0989_Mailchimp_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Mailchimp for data processing. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Mailchimp,  

---

### Manual Hunter Automate Triggered
**Filename:** `0991_Manual_Hunter_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Hunter for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Hunter,  

---

### Manual Mailjet Automate Triggered
**Filename:** `0993_Manual_Mailjet_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Mailjet for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Mailjet,  

---

### Mailjet Automate Triggered
**Filename:** `0994_Mailjet_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Mailjet for data processing. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Mailjet,  

---

### Manual Mautic Automate Triggered
**Filename:** `1017_Manual_Mautic_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Mautic for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Mautic,  

---

### Manual Mandrill Automate Triggered
**Filename:** `1037_Manual_Mandrill_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Mandrill for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Mandrill,  

---

### Manual Emailsend Send Triggered
**Filename:** `1047_Manual_Emailsend_Send_Triggered.json`  
**Description:** Manual workflow that integrates with Emailsend for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Emailsend,  

---

### Emailreadimap Send
**Filename:** `1050_Emailreadimap_Send.json`  
**Description:** Manual workflow that integrates with Email (IMAP) for data processing. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (1 nodes)  
**Integrations:** Email (IMAP),  

---

### Mautic Googlesheets Automate Scheduled
**Filename:** `1083_Mautic_GoogleSheets_Automate_Scheduled.json`  
**Description:** Scheduled automation that connects Google Sheets and Mautic for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** low (4 nodes)  
**Integrations:** Google Sheets,Mautic,  

---

### Sse Automation Triggered
**Filename:** `1084_Sse_Automation_Triggered.json`  
**Description:** Webhook-triggered automation that integrates with Server-Sent Events for data processing. Uses 1 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (1 nodes)  
**Integrations:** Server-Sent Events,  

---

### Create a new list, add a new contact to the list, update the contact, and get all contacts in the list
**Filename:** `1154_Manual_Automizy_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Automizy to create new records. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Automizy,  

---

### New WooCommerce Customer to Mautic
**Filename:** `1160_Mautic_Woocommerce_Create_Triggered.json`  
**Description:** Webhook-triggered automation that connects Mautic and Woocommerce for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (5 nodes)  
**Integrations:** Mautic,Woocommerce,  

---

### Check for valid Mautic contact email
**Filename:** `1168_Mautic_Slack_Send_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Onesimpleapi, Slack, and Mautic for data processing. Uses 6 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (6 nodes)  
**Integrations:** Onesimpleapi,Slack,Mautic,Form Trigger,  

---

### Sending an SMS using sms77
**Filename:** `1199_Manual_Sms77_Send_Triggered.json`  
**Description:** Manual workflow that integrates with Sms77 for data processing. Uses 2 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (2 nodes)  
**Integrations:** Sms77,  

---

### Getresponse Airtable Import Triggered
**Filename:** `1202_Getresponse_Airtable_Import_Triggered.json`  
**Description:** Webhook-triggered automation that connects Airtable and Getresponse for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Airtable,Getresponse,  

---

### Manual Tapfiliate Automate Triggered
**Filename:** `1205_Manual_Tapfiliate_Automate_Triggered.json`  
**Description:** Manual workflow that integrates with Tapfiliate for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Tapfiliate,  

---

### Emelia Automate
**Filename:** `1214_Emelia_Automate.json`  
**Description:** Manual workflow that integrates with Emelia for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (3 nodes)  
**Integrations:** Emelia,  

---

### Create, update and get a subscriber using the MailerLite node
**Filename:** `1218_Manual_Mailerlite_Create_Triggered.json`  
**Description:** Manual workflow that integrates with Mailerlite to create new records. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Mailerlite,  

---

### Autopilot Automate
**Filename:** `1227_Autopilot_Automate.json`  
**Description:** Manual workflow that integrates with Autopilot for data processing. Uses 4 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (4 nodes)  
**Integrations:** Autopilot,  

---

### Autopilot Airtable Automate Triggered
**Filename:** `1228_Autopilot_Airtable_Automate_Triggered.json`  
**Description:** Webhook-triggered automation that connects Airtable and Autopilot for data processing. Uses 3 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** low (3 nodes)  
**Integrations:** Airtable,Autopilot,  

---

### Very simple Human in the loop system email with AI e IMAP
**Filename:** `1240_Markdown_Stickynote_Send.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, OpenAI, and Email (IMAP) for data processing. Uses 16 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** Markdown,OpenAI,Email (IMAP),Agent,Emailsend,Chainsummarization,  

---

### Email AI Auto-responder. Summerize and send email
**Filename:** `1277_Emailreadimap_Manual_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Textsplittertokensplitter, and OpenAI for data processing. Uses 26 nodes and integrates with 14 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (26 nodes)  
**Integrations:** Markdown,Textsplittertokensplitter,OpenAI,Google Drive,Lmchatopenai,Email (IMAP),Agent,Emailsend,Httprequest,Chainllm,Vectorstoreqdrant,Documentdefaultdataloader,Chainsummarization,Textclassifier,  

---

### AI Email processing autoresponder with approval (Yes/No)
**Filename:** `1284_Emailreadimap_Markdown_Send.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, OpenAI, and Lmchatopenai for data processing. Uses 17 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Markdown,OpenAI,Lmchatopenai,Email (IMAP),Agent,Gmail,Emailsend,Vectorstoreqdrant,Chainsummarization,  

---

### Code Converttofile Send Webhook
**Filename:** `1307_Code_Converttofile_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Converttofile, OpenAI, and Gmail for data processing. Uses 25 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (25 nodes)  
**Integrations:** Converttofile,OpenAI,Gmail,Jira,Httprequest,Outlook,Form Trigger,  

---

### Filter Manual Send Triggered
**Filename:** `1321_Filter_Manual_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Splitinbatches, and Agent for data processing. Uses 36 nodes and integrates with 5 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (36 nodes)  
**Integrations:** Markdown,Splitinbatches,Agent,Outlook,Lmchatollama,  

---

### Lemlist Slack Automate Webhook
**Filename:** `1382_Lemlist_Slack_Automate_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates OpenAI, Lemlist, and Outputparserstructured for data processing. Uses 18 nodes and integrates with 7 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** OpenAI,Lemlist,Outputparserstructured,Httprequest,Chainllm,Form Trigger,Slack,  

---

### Send Emails from Obsidian
**Filename:** `1403_Splitout_Datetime_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Splitinbatches, Converttofile, and Datetime for data processing. Uses 19 nodes and integrates with 7 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (19 nodes)  
**Integrations:** Splitinbatches,Converttofile,Datetime,Splitout,Respondtowebhook,Gmail,Webhook,  

---

### Send
**Filename:** `1409_Send.json`  
**Description:** Manual workflow that for data processing. Uses 0 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (0 nodes)  
**Integrations:** None  

---

### Effortless Email Management with AI
**Filename:** `1427_Emailreadimap_Manual_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Textsplittertokensplitter, and OpenAI for data processing. Uses 31 nodes and integrates with 14 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (31 nodes)  
**Integrations:** Markdown,Textsplittertokensplitter,OpenAI,Google Drive,Email (IMAP),Agent,Gmail,Emailsend,Httprequest,Lmchatdeepseek,Documentdefaultdataloader,Vectorstoreqdrant,Chainsummarization,Textclassifier,  

---

### Code Schedule Send Scheduled
**Filename:** `1428_Code_Schedule_Send_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Editimage, OpenAI, and Lmchatgroq for data processing. Uses 32 nodes and integrates with 11 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (32 nodes)  
**Integrations:** Editimage,OpenAI,Lmchatgroq,Airtable,Agent,Gmail,Executiondata,Form Trigger,Executeworkflow,Toolwikipedia,Memorybufferwindow,  

---

### Code Schedule Send Scheduled
**Filename:** `1429_Code_Schedule_Send_Scheduled.json`  
**Description:** Complex multi-step automation that orchestrates Editimage, OpenAI, and Lmchatgroq for data processing. Uses 32 nodes and integrates with 11 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (32 nodes)  
**Integrations:** Editimage,OpenAI,Lmchatgroq,Airtable,Agent,Gmail,Executiondata,Form Trigger,Executeworkflow,Toolwikipedia,Memorybufferwindow,  

---

### Email Summary Agent
**Filename:** `1430_Aggregate_Schedule_Send_Scheduled.json`  
**Description:** Scheduled automation that connects Gmail and OpenAI for data processing. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (9 nodes)  
**Integrations:** Gmail,OpenAI,  

---

### [hiroshidigital.com] Send Message In Larksuite
**Filename:** `1505_Manual_Stickynote_Send_Webhook.json`  
**Description:** Manual workflow that integrates with Httprequest for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,  

---

### Email verification with Icypeas (single)
**Filename:** `1516_Manual_Stickynote_Send_Webhook.json`  
**Description:** Manual workflow that integrates with Httprequest for data processing. Uses 6 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** medium (6 nodes)  
**Integrations:** Httprequest,  

---

### Shopify + Mautic
**Filename:** `1526_Mautic_Webhook_Automation_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Shopify, Crypto, and GraphQL for data processing. Uses 26 nodes and integrates with 5 services.  
**Status:** Active  
**Trigger:** Complex  
**Complexity:** high (26 nodes)  
**Integrations:** Shopify,Crypto,GraphQL,Webhook,Mautic,  

---

### Email Summary Agent
**Filename:** `1544_Aggregate_Schedule_Send_Scheduled.json`  
**Description:** Scheduled automation that connects Gmail and OpenAI for data processing. Uses 9 nodes.  
**Status:** Inactive  
**Trigger:** Scheduled  
**Complexity:** medium (9 nodes)  
**Integrations:** Gmail,OpenAI,  

---

### Very simple Human in the loop system email with AI e IMAP
**Filename:** `1571_Markdown_Stickynote_Send.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, OpenAI, and Email (IMAP) for data processing. Uses 16 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (16 nodes)  
**Integrations:** Markdown,OpenAI,Email (IMAP),Agent,Emailsend,Chainsummarization,  

---

### AI Email processing autoresponder with approval (Yes/No)
**Filename:** `1588_Emailreadimap_Markdown_Send.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, OpenAI, and Lmchatopenai for data processing. Uses 17 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (17 nodes)  
**Integrations:** Markdown,OpenAI,Lmchatopenai,Email (IMAP),Agent,Gmail,Emailsend,Vectorstoreqdrant,Chainsummarization,  

---

### The Easiest Way to Send SMS Worldwide
**Filename:** `1616_Manual_Stickynote_Send_Webhook.json`  
**Description:** Manual workflow that integrates with Httprequest for data processing. Uses 5 nodes.  
**Status:** Inactive  
**Trigger:** Manual  
**Complexity:** low (5 nodes)  
**Integrations:** Httprequest,  

---

### Wait Splitout Send Webhook
**Filename:** `1638_Wait_Splitout_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Lmchatgooglegemini, Splitout, and Agent for data processing. Uses 35 nodes and integrates with 9 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (35 nodes)  
**Integrations:** Lmchatgooglegemini,Splitout,Agent,Httprequest,Chainllm,WhatsApp,Form Trigger,Toolwikipedia,Memorybufferwindow,  

---

### Scrape Books from URL with Dumpling AI, Clean HTML, Save to Sheets, Email as CSV
**Filename:** `1648_Splitout_Converttofile_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Converttofile, Google Sheets, and Splitout for data processing. Uses 11 nodes and integrates with 6 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** medium (11 nodes)  
**Integrations:** Converttofile,Google Sheets,Splitout,Gmail,Html,Httprequest,  

---

### Code Webhook Send Webhook
**Filename:** `1653_Code_Webhook_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Crypto, OpenAI, and Google Sheets for data processing. Uses 49 nodes and integrates with 8 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (49 nodes)  
**Integrations:** Crypto,OpenAI,Google Sheets,Webhook,Gmail,Html,Respondtowebhook,Form Trigger,  

---

### Code Readpdf Send Triggered
**Filename:** `1656_Code_Readpdf_Send_Triggered.json`  
**Description:** Complex multi-step automation that orchestrates Gmail, OpenAI, and Google Drive for data processing. Uses 18 nodes and integrates with 4 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (18 nodes)  
**Integrations:** Gmail,OpenAI,Google Drive,Readpdf,  

---

### Gumroad sale trigger
**Filename:** `1874_Mailerlite_Gumroad_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that orchestrates Httprequest, Gumroad, and Google Sheets for data processing. Uses 8 nodes and integrates with 4 services.  
**Status:** Active  
**Trigger:** Webhook  
**Complexity:** medium (8 nodes)  
**Integrations:** Httprequest,Gumroad,Google Sheets,Mailerlite,  

---

### Wordpress Form to Mautic
**Filename:** `1892_Noop_Mautic_Automation_Webhook.json`  
**Description:** Webhook-triggered automation that connects Mautic and Form Trigger for data processing. Uses 10 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (10 nodes)  
**Integrations:** Mautic,Form Trigger,  

---

### Effortless Email Management with AI
**Filename:** `1936_Emailreadimap_Manual_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Textsplittertokensplitter, and OpenAI for data processing. Uses 31 nodes and integrates with 14 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (31 nodes)  
**Integrations:** Markdown,Textsplittertokensplitter,OpenAI,Google Drive,Email (IMAP),Agent,Gmail,Emailsend,Httprequest,Lmchatdeepseek,Documentdefaultdataloader,Vectorstoreqdrant,Chainsummarization,Textclassifier,  

---

### Forward Netflix emails to multiple email addresses with GMail and Mailjet
**Filename:** `1956_Mailjet_Gmail_Create_Triggered.json`  
**Description:** Webhook-triggered automation that orchestrates Mailjet, Splitout, and Gmail for data processing. Uses 7 nodes.  
**Status:** Inactive  
**Trigger:** Webhook  
**Complexity:** medium (7 nodes)  
**Integrations:** Mailjet,Splitout,Gmail,  

---

### Email AI Auto-responder. Summerize and send email
**Filename:** `1962_Emailreadimap_Manual_Send_Webhook.json`  
**Description:** Complex multi-step automation that orchestrates Markdown, Textsplittertokensplitter, and OpenAI for data processing. Uses 26 nodes and integrates with 14 services.  
**Status:** Inactive  
**Trigger:** Complex  
**Complexity:** high (26 nodes)  
**Integrations:** Markdown,Textsplittertokensplitter,OpenAI,Google Drive,Lmchatopenai,Email (IMAP),Agent,Emailsend,Httprequest,Chainllm,Vectorstoreqdrant,Documentdefaultdataloader,Chainsummarization,Textclassifier,  

---


## Summary

**Total Marketing & Advertising Automation workflows:** 143  
**Documentation generated:** 2025-07-27 14:36:56  
**API Source:** https://scan-might-updates-postage.trycloudflare.com/api  

This documentation was automatically generated using the n8n workflow API endpoints.
